﻿2025-08-07 10:05:01.991 | INFO     | src.modules.format_management.format_renderer:render_dataframe:107 | 🎯 [格式渲染] 隐藏字段配置: ['created_at', 'id', 'updated_at', 'sequence', 'row_number']
2025-08-07 10:05:01.991 | INFO     | src.modules.format_management.format_renderer:render_dataframe:115 | 🎯 [格式渲染] 无需隐藏字段（未在当前数据中找到）
2025-08-07 10:05:01.996 | INFO     | src.modules.format_management.format_renderer:render_dataframe:133 | [关键修复] 字 段: 补发 -> 英文名: supplement -> 类型: float
2025-08-07 10:05:01.996 | INFO     | src.modules.format_management.format_renderer:render_dataframe:133 | [关键修复] 字 段: 借支 -> 英文名: advance -> 类型: float
2025-08-07 10:05:01.998 | INFO     | src.modules.format_management.format_renderer:render_dataframe:133 | [关键修复] 字 段: 月份 -> 英文名: month -> 类型: month_string
2025-08-07 10:05:01.998 | INFO     | src.modules.format_management.field_registry:get_display_fields:1553 | 🔧 [P0-2修复] display_order转换完成: 24个字段，原始字段数: 24
2025-08-07 10:05:01.999 | INFO     | src.modules.format_management.format_renderer:render_dataframe:153 | 🔧 [DEBUG] table_type=active_employees, display_fields=24个字段
2025-08-07 10:05:01.999 | INFO     | src.modules.format_management.format_renderer:render_dataframe:174 | 🔧 [DEBUG] formatted_df.columns=24个, existing_display_fields=24个
2025-08-07 10:05:02.000 | INFO     | src.modules.format_management.format_renderer:render_dataframe:178 | 🎯 [格式渲染] 已按display_order排列字段: 24个字段
2025-08-07 10:05:02.000 | INFO     | src.modules.format_management.format_renderer:render_dataframe:199 | 🎨 [格式渲染] DataFrame格式化完成: active_employees, 行数: 50, 列数: 24
2025-08-07 10:05:02.001 | INFO     | src.modules.format_management.unified_format_manager:format_data:363 | 🎯 [统一格式管理] 数据格式化完成: active_employees, 行数: 50, 列数: 24
QBasicTimer::start: QBasicTimer can only be used with threads started with QThread
QObject::connect: Cannot queue arguments of type 'Qt::Orientation'
(Make sure 'Qt::Orientation' is registered using qRegisterMetaType().)
QObject::connect: Cannot queue arguments of type 'Qt::Orientation'
(Make sure 'Qt::Orientation' is registered using qRegisterMetaType().)
QObject::connect: Cannot queue arguments of type 'Qt::Orientation'
(Make sure 'Qt::Orientation' is registered using qRegisterMetaType().)
QObject::connect: Cannot queue arguments of type 'Qt::Orientation'
(Make sure 'Qt::Orientation' is registered using qRegisterMetaType().)
QObject::connect: Cannot queue arguments of type 'Qt::Orientation'
(Make sure 'Qt::Orientation' is registered using qRegisterMetaType().)
2025-08-07 10:05:02.025 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:238 | [数据流追踪] 自动渲染策略选择: 50行数据
2025-08-07 10:05:02.025 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_small_dataset:51 | [数据 流追踪] 开始小数据集渲染: 50行 x 24列
QObject::connect: Cannot queue arguments of type 'QVector<int>'
(Make sure 'QVector<int>' is registered using qRegisterMetaType().)
QObject::connect: Cannot queue arguments of type 'QVector<int>'
(Make sure 'QVector<int>' is registered using qRegisterMetaType().)
QObject::connect: Cannot queue arguments of type 'QVector<int>'
(Make sure 'QVector<int>' is registered using qRegisterMetaType().)
QObject::connect: Cannot queue arguments of type 'QVector<int>'
(Make sure 'QVector<int>' is registered using qRegisterMetaType().)
QObject::connect: Cannot queue arguments of type 'QVector<int>'
(Make sure 'QVector<int>' is registered using qRegisterMetaType().)
2025-08-07 10:05:02.042 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_small_dataset:104 | [数据流追踪] 小数据集渲染完成: 耗时17.0ms, 平均每行0.34ms
2025-08-07 10:05:02.042 | INFO     | src.core.performance_metrics_collector:record_operation:85 | [数据流追踪] 记录性能 度量: render, 数据大小=50, 渲染时间=17.0ms, 策略=small_dataset
2025-08-07 10:05:02.043 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:259 | [数据流追踪] 选择小数据集渲染策略
2025-08-07 10:05:02.043 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2778 | [数据流追踪] 优化渲染完成: 50行 x 24列, 策略=small_dataset, 耗时=17.0ms, 性能评级=excellent
2025-08-07 10:05:02.044 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:430 | 第0行数据工号: 20191787
2025-08-07 10:05:02.044 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:430 | 第1行数据工号: 20241009
2025-08-07 10:05:02.044 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:430 | 第2行数据工号: 20021464
2025-08-07 10:05:02.045 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:430 | 第3行数据工号: 20121440
2025-08-07 10:05:02.045 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:430 | 第4行数据工号: 20251012
2025-08-07 10:05:02.046 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:458 |   [UI数据修复] _update_visible_rows调用，数据行数: 50
2025-08-07 10:05:02.046 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:471 |   [UI数据修复] row_data[0]: 工号=20191787, 薪资=999.00
2025-08-07 10:05:02.046 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:471 |   [UI数据修复] row_data[1]: 工号=20241009, 薪资=1339.00
2025-08-07 10:05:02.047 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:471 |   [UI数据修复] row_data[2]: 工号=20021464, 薪资=2175.00
2025-08-07 10:05:02.047 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:471 |   [UI数据修复] row_data[3]: 工号=20121440, 薪资=1427.00
2025-08-07 10:05:02.047 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:471 |   [UI数据修复] row_data[4]: 工号=20251012, 薪资=934.00
2025-08-07 10:05:02.048 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:472 |  可见行数据顺序: ['20191787', '20241009', '20021464', '20121440', '20251012']
2025-08-07 10:05:02.048 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 表格数据已设 置: 50 行, 24 列
2025-08-07 10:05:02.049 | WARNING  | src.utils.thread_safe_timer:is_main_thread:63 | 🔧 [线程检查] 当前线程不是主线程: ThreadPoolExecutor-2_0
2025-08-07 10:05:02.049 | WARNING  | src.utils.thread_safe_timer:safe_single_shot:158 | 🔧 [线程安全] 尝试在非主线程使用singleShot，将移动到主线程执行
2025-08-07 10:05:02.049 | WARNING  | src.utils.thread_safe_timer:is_main_thread:63 | 🔧 [线程检查] 当前线程不是主线程: ThreadPoolExecutor-2_0
QObject::startTimer: Timers cannot be started from another thread
2025-08-07 10:05:02.055 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2870 | 表格数据设置完成: 50 行, 耗时: 69.0ms
2025-08-07 10:05:02.056 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:7661 | 🔧 [P0-排序修复] 开始恢复排序指示器显示
2025-08-07 10:05:02.056 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:7671 | 🔧 [P0-排序修复] 恢复排序指示器成功: 1 列排序
2025-08-07 10:05:02.057 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:7672 | 🔧 [P0-排序修复] 排序描述: 2025年岗位工资: 升序
QBasicTimer::start: QBasicTimer can only be used with threads started with QThread
QBasicTimer::start: QBasicTimer can only be used with threads started with QThread
2025-08-07 10:05:02.068 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1719 | 列宽已恢复: salary_data_2025_08_active_employees (24/24 列)
2025-08-07 10:05:02.068 | WARNING  | src.utils.thread_safe_timer:is_main_thread:63 | 🔧 [线程检查] 当前线程不是主线程: ThreadPoolExecutor-2_0
2025-08-07 10:05:02.069 | WARNING  | src.utils.thread_safe_timer:safe_single_shot:158 | 🔧 [线程安全] 尝试在非主线程使用singleShot，将移动到主线程执行
2025-08-07 10:05:02.069 | WARNING  | src.utils.thread_safe_timer:is_main_thread:63 | 🔧 [线程检查] 当前线程不是主线程: ThreadPoolExecutor-2_0
QObject::startTimer: Timers cannot be started from another thread
2025-08-07 10:05:02.071 | INFO     | src.gui.widgets.pagination_widget:set_total_records:436 | 总记录数设置为: 50
2025-08-07 10:05:02.071 | INFO     | src.gui.prototype.prototype_main_window:set_data:847 | 🔧 [P0-CRITICAL修复] 非分页 上下文，设置total_records=50
QObject::startTimer: Timers can only be used with threads started with QThread
QObject::startTimer: Timers can only be used with threads started with QThread
2025-08-07 10:05:02.077 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3515 | 🔧 [P1-1修复]  数据已成功设置到UI，使用新映射表头: 50行, 24列
2025-08-07 10:05:02.077 | INFO     | src.gui.prototype.widgets.header_update_manager:force_update_all:317 | 智能强制更新: 表头 24 个, 行号起始 1, 共 50 行
2025-08-07 10:05:02.078 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3531 | 🔧 [P0-新5修复] 表头强制刷新完成: salary_data_2025_08_active_employees, 传递参数: 24个表头
2025-08-07 10:05:02.078 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3535 | 🔧 [统一数据设 置] 数据已成功设置到UI: 50行, 24列
2025-08-07 10:05:02.078 | INFO     | src.gui.widgets.pagination_widget:set_total_records:436 | 总记录数设置为: 1396
2025-08-07 10:05:02.079 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3558 | 🔧 [分页修复]  数据更新事件设置总记录数: 1396
2025-08-07 10:05:02.079 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3565 | 🔧 [CRITICAL修 复] 排序操作保持当前页码: 1，不进行页码重置