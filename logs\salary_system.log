2025-08-07 10:03:43.011 | INFO     | src.utils.log_config:_log_initialization_info:227 | 日志系统初始化完成
2025-08-07 10:03:43.012 | INFO     | src.utils.log_config:_log_initialization_info:228 | 日志级别: INFO
2025-08-07 10:03:43.012 | INFO     | src.utils.log_config:_log_initialization_info:229 | 控制台输出: True
2025-08-07 10:03:43.013 | INFO     | src.utils.log_config:_log_initialization_info:230 | 文件输出: True
2025-08-07 10:03:43.013 | INFO     | src.utils.log_config:_log_initialization_info:236 | 日志文件路径: logs/salary_system.log
2025-08-07 10:03:43.013 | INFO     | src:<module>:20 | 月度工资异动处理系统 v1.0.0 初始化完成
2025-08-07 10:03:45.751 | INFO     | src.gui.style_manager:__init__:68 | StyleManager初始化完成
2025-08-07 10:03:45.752 | INFO     | src.gui.style_manager:apply_global_style:93 | 开始应用全局Material Design样式...
2025-08-07 10:03:45.752 | INFO     | src.gui.style_manager:apply_global_style:105 | 全局样式应用成功
2025-08-07 10:03:45.752 | INFO     | src.gui.style_manager:enable_hot_reload:416 | 样式热重载未启用（非开发环境）
2025-08-07 10:03:45.752 | INFO     | __main__:setup_qt_exception_handling:218 | 🔧 [P0-修复] PyQt专用异常处理机制已启用
2025-08-07 10:03:45.753 | INFO     | __main__:setup_app_logging:349 | 月度工资异动处理系统 v2.0.0-refactored 启动
2025-08-07 10:03:45.753 | INFO     | __main__:main:413 | 初始化核心管理器...
2025-08-07 10:03:45.753 | INFO     | src.modules.system_config.config_manager:__init__:313 | 配置管理器初始化完成，配置文件: config.json
2025-08-07 10:03:45.754 | INFO     | src.modules.system_config.config_manager:load_config:340 | 正在加载配置文件: config.json
2025-08-07 10:03:45.754 | INFO     | src.modules.system_config.config_manager:_generate_validation_result:252 | 配置文件验证通过
2025-08-07 10:03:45.755 | INFO     | src.modules.system_config.config_manager:load_config:352 | 配置文件加载成功
2025-08-07 10:03:45.755 | INFO     | src.modules.data_storage.database_manager:_initialize_database:101 | 开始初始化数据库...
2025-08-07 10:03:45.764 | INFO     | src.modules.data_storage.database_manager:_initialize_database:156 | 数据库初始化完成
2025-08-07 10:03:45.765 | INFO     | src.modules.data_storage.database_manager:__init__:97 | 数据库管理器初始化完成，数据库路径: E:\project\case\salary_changes\salary_changes\data\db\salary_system.db
2025-08-07 10:03:45.766 | INFO     | src.modules.system_config.config_manager:__init__:313 | 配置管理器初始化完成，配置文件: config.json
2025-08-07 10:03:45.767 | INFO     | src.modules.data_storage.dynamic_table_manager:__init__:104 | 动态表管理器初始化完成
2025-08-07 10:03:45.767 | INFO     | __main__:main:418 | 核心管理器初始化完成。
2025-08-07 10:03:45.772 | INFO     | src.core.data_source_unification_manager:__init__:128 | 数据源统一管理器初始化完成
2025-08-07 10:03:45.773 | INFO     | src.core.table_sort_state_manager:__init__:174 | 表级排序状态管理器初始化完成
2025-08-07 10:03:45.773 | INFO     | src.core.event_bus:__init__:227 | 事件总线初始化完成
2025-08-07 10:03:45.773 | INFO     | src.core.error_handler_manager:__init__:111 | 🔧 [P2-3] 错误处理管理器初始化完成
2025-08-07 10:03:45.774 | INFO     | src.core.error_handler_manager:register_recovery_strategy:372 | 🔧 [P2-3] 注册恢复策略: data_error_medium
2025-08-07 10:03:45.774 | INFO     | src.core.error_handler_manager:register_recovery_strategy:372 | 🔧 [P2-3] 注册恢复策略: ui_error_medium
2025-08-07 10:03:45.775 | INFO     | src.core.error_handler_manager:register_recovery_strategy:372 | 🔧 [P2-3] 注册恢复策略: system_error_medium
2025-08-07 10:03:45.778 | INFO     | src.gui.prototype.prototype_main_window:_register_recovery_strategies:10291 | 🔧 [P2-3] 错误恢复策略注册完成
2025-08-07 10:03:45.779 | INFO     | src.core.event_bus:__init__:227 | 事件总线初始化完成
2025-08-07 10:03:45.779 | INFO     | src.gui.prototype.prototype_main_window:_setup_error_handling:10146 | 🔧 [P2-3] 错误处理机制设置完成
2025-08-07 10:03:45.780 | INFO     | src.gui.prototype.prototype_main_window:_init_recursive_call_protection:10184 | 🔧 [P1-2] 递归调用防护机制初始化完成
2025-08-07 10:03:45.794 | INFO     | src.core.architecture_factory:__init__:62 | 架构重构工厂初始化完成
2025-08-07 10:03:45.795 | INFO     | src.core.architecture_factory:initialize_architecture:72 | 开始初始化架构重构系统...
2025-08-07 10:03:45.795 | INFO     | src.core.event_bus:__init__:227 | 事件总线初始化完成
2025-08-07 10:03:45.900 | INFO     | src.modules.data_import.config_sync_manager:_do_config_initialization:139 | 🔧 [配置修复] 创建了完整的默认配置，包含基本字段映射和模板
2025-08-07 10:03:45.901 | INFO     | src.modules.data_import.config_sync_manager:__init__:81 | 🆕 [新架构] 配置同步管理器初始化完成（依赖注入）
2025-08-07 10:03:45.907 | INFO     | src.core.unified_state_manager:_load_state:554 | 状态文件不存在，使用默认状态
2025-08-07 10:03:45.908 | INFO     | src.core.unified_state_manager:__init__:181 | 统一状态管理器初始化完成
2025-08-07 10:03:45.909 | INFO     | src.core.unified_data_request_manager:__init__:190 | 统一数据请求管理器初始化完成
2025-08-07 10:03:45.910 | INFO     | src.core.unified_data_request_manager:__init__:190 | 统一数据请求管理器初始化完成
2025-08-07 10:03:45.911 | INFO     | src.core.unified_state_manager:_load_state:554 | 状态文件不存在，使用默认状态
2025-08-07 10:03:45.912 | INFO     | src.core.unified_state_manager:__init__:181 | 统一状态管理器初始化完成
2025-08-07 10:03:45.913 | INFO     | src.services.table_data_service:__init__:80 | 表格数据服务初始化完成
2025-08-07 10:03:45.914 | INFO     | src.core.architecture_factory:initialize_architecture:103 | 🎉 架构重构系统初始化成功！耗时: 118.9ms
2025-08-07 10:03:45.931 | INFO     | src.gui.prototype.performance.data_preload_cache:__init__:46 | 数据预加载缓存初始化完成 - 最大条目数: 100, TTL: 300秒
2025-08-07 10:03:45.932 | INFO     | src.gui.prototype.performance.data_preload_cache:__init__:232 | 表格状态缓存初始化完成
2025-08-07 10:03:45.935 | INFO     | src.gui.prototype.performance.header_config_cache:__init__:32 | 表头配置缓存初始化完成
2025-08-07 10:03:45.936 | INFO     | src.gui.prototype.performance.header_config_cache:__init__:211 | 字段映射缓存初始化完成
2025-08-07 10:03:45.937 | INFO     | src.gui.prototype.performance.performance_manager:__init__:46 | 🚀 性能管理器初始化完成
2025-08-07 10:03:45.938 | INFO     | src.gui.prototype.prototype_main_window:__init__:3262 | 🚀 性能管理器已集成
2025-08-07 10:03:45.939 | INFO     | src.gui.prototype.prototype_main_window:__init__:3264 | ✅ 新架构集成成功！
2025-08-07 10:03:45.941 | INFO     | src.gui.prototype.prototype_main_window:_inject_config_sync_manager_to_existing_tables:3376 | ConfigSyncManager重新注入完成，已更新0个表格实例
2025-08-07 10:03:45.941 | INFO     | src.gui.prototype.prototype_main_window:_setup_new_architecture_listeners:3342 | ✅ 新架构事件监听器设置完成
2025-08-07 10:03:45.942 | INFO     | src.gui.widgets.pagination_cache_manager:__init__:107 | 分页缓存管理器初始化完成 - 最大条目数: 50, 最大内存: 50MB
2025-08-07 10:03:45.944 | INFO     | src.gui.prototype.managers.responsive_layout_manager:__init__:121 | 响应式布局管理器初始化完成
2025-08-07 10:03:45.953 | INFO     | src.gui.prototype.managers.communication_manager:__init__:134 | 组件通信管理器初始化完成
2025-08-07 10:03:46.392 | INFO     | src.gui.prototype.prototype_main_window:_create_menu_bar:2475 | 菜单栏创建完成
2025-08-07 10:03:46.392 | INFO     | src.modules.system_config.user_preferences:__init__:61 | 用户偏好设置管理器初始化完成，偏好文件: E:\project\case\salary_changes\salary_changes\user_preferences.json
2025-08-07 10:03:46.394 | INFO     | src.modules.system_config.user_preferences:load_preferences:183 | 正在加载偏好设置: user_preferences.json
2025-08-07 10:03:46.396 | INFO     | src.modules.system_config.user_preferences:load_preferences:193 | 偏好设置加载成功
2025-08-07 10:03:46.396 | INFO     | src.gui.prototype.prototype_main_window:__init__:2451 | 菜单栏管理器初始化完成
2025-08-07 10:03:46.396 | INFO     | src.gui.table_header_manager:__init__:104 | 🔧 [P1-3] 性能优化表头管理器初始化完成
2025-08-07 10:03:46.397 | INFO     | src.gui.prototype.prototype_main_window:_setup_managers:5025 | 管理器设置完成，包含增强版表头管理器
2025-08-07 10:03:46.397 | INFO     | src.gui.prototype.prototype_main_window:_apply_window_style:5030 | 🔧 开始应用窗口级Material Design样式...
2025-08-07 10:03:46.398 | INFO     | src.gui.style_manager:apply_window_style:123 | 开始应用窗口级样式...
2025-08-07 10:03:46.400 | INFO     | src.gui.style_manager:apply_window_style:136 | 窗口级样式应用成功
2025-08-07 10:03:46.400 | INFO     | src.gui.prototype.prototype_main_window:_apply_window_style:5037 | ✅ 窗口级样式应用成功
2025-08-07 10:03:46.400 | INFO     | src.gui.prototype.prototype_main_window:_setup_responsive_style:5078 | ✅ 响应式样式监听设置完成
2025-08-07 10:03:46.404 | INFO     | src.gui.prototype.widgets.smart_search_debounce:__init__:368 | 智能防抖搜索算法初始化完成
2025-08-07 10:03:46.411 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:__init__:148 | 智能树形展开算法初始化完成
2025-08-07 10:03:46.416 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_load_dynamic_salary_data:1298 | 开始从元数据动态加载工资数据...
2025-08-07 10:03:46.417 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_load_dynamic_salary_data:1305 | 检测到首次启动，将延迟加载导航数据确保数据库就绪
2025-08-07 10:03:46.419 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_setup_navigation_data:752 | 导航面板已重构：移除功能性导航，专注数据导航
2025-08-07 10:03:46.420 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_restore_state:785 | 恢复导航状态: 0个展开项
2025-08-07 10:03:46.420 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['工资表', '工资表 > 2025年']
2025-08-07 10:03:46.420 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:__init__:529 | 增强导航面板初始化完成
2025-08-07 10:03:46.439 | INFO     | src.gui.prototype.widgets.header_update_manager:__init__:68 | HeaderUpdateManager 初始化完成，关联表格: VirtualizedExpandableTable
2025-08-07 10:03:46.440 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:2109 | 🚨 [架构修复] 启用模型数据同步机制
2025-08-07 10:03:46.442 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_register_shortcuts:1352 | 快捷键注册完成: 18/18 个
2025-08-07 10:03:46.442 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:1776 | 拖拽排序管理器初始化完成
2025-08-07 10:03:46.449 | INFO     | src.modules.data_management.data_flow_validator:__init__:78 | 🔧 [数据验证器] 初始化完成，验证级别: moderate
2025-08-07 10:03:46.450 | INFO     | src.modules.state_management.table_state_manager:__init__:103 | 🔧 [状态管理] 仅使用内存缓存
2025-08-07 10:03:46.452 | INFO     | src.modules.state_management.table_state_manager:__init__:105 | 🔧 [状态管理] 初始化完成，最大缓存: 100
2025-08-07 10:03:46.452 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:2144 | 🔧 [排序修复] 数据流验证器和状态管理器初始化成功
2025-08-07 10:03:46.452 | INFO     | src.modules.data_import.header_edit_manager:__init__:82 | 表头编辑管理器初始化完成
2025-08-07 10:03:46.453 | INFO     | src.core.unified_state_manager:_load_state:554 | 状态文件不存在，使用默认状态
2025-08-07 10:03:46.453 | INFO     | src.core.unified_state_manager:__init__:181 | 统一状态管理器初始化完成
2025-08-07 10:03:46.454 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:2196 | 🎯 [统一格式管理] 使用统一格式管理器
2025-08-07 10:03:46.465 | INFO     | src.gui.prototype.widgets.column_sort_manager:_load_field_mappings_from_config:337 | 🔧 [新架构] 成功加载 46 个字段映射
2025-08-07 10:03:46.465 | INFO     | src.gui.prototype.widgets.column_sort_manager:__init__:99 | 🆕 [新架构排序] 多列排序管理器初始化完成，最大排序列数: 3
2025-08-07 10:03:46.465 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:2243 | 🆕 [新架构多列排序] 初始化完成，支持最多3列排序
2025-08-07 10:03:46.466 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:1536 | 🔧 [列宽保存修复] 列宽管理器初始化完成
2025-08-07 10:03:46.466 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:1537 | 🔧 [列宽保存修复] 配置文件绝对路径: E:\project\case\salary_changes\salary_changes\state\column_widths.json
2025-08-07 10:03:46.467 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:1538 | 🔧 [列宽保存修复] 配置文件存在: False
2025-08-07 10:03:46.467 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:1539 | 🔧 [列宽保存修复] 父目录存在: True
2025-08-07 10:03:46.468 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:1540 | 🔧 [列宽保存修复] 当前工作目录: E:\project\case\salary_changes\salary_changes
2025-08-07 10:03:46.468 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:2250 | 列宽管理器初始化完成
2025-08-07 10:03:46.468 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:setup_ui:2366 | 🔧 [架构修复] 保持自定义排序功能，修复数据显示问题
2025-08-07 10:03:46.469 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:2264 | 虚拟化可展开表格初始化完成 - Phase 4核心交互功能增强版，最大可见行数: 1000
2025-08-07 10:03:46.473 | WARNING  | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:2113 | 无法获取主窗口引用，使用备用方案显示空表格
2025-08-07 10:03:46.473 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_validate_and_fix_data_consistency:4227 | 🔧 [数据修复] 数据或表头为空，跳过一致性验证
2025-08-07 10:03:46.474 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:5040 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-08-07 10:03:46.474 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2699 | 表格格式化完成: default_table, 类型: active_employees
2025-08-07 10:03:46.478 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:238 | [数据流追踪] 自动渲染策略选择: 0行数据
2025-08-07 10:03:46.479 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:242 | [数据流追踪] 检测到空数据，使用快速清空策略
2025-08-07 10:03:46.479 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2778 | [数据流追踪] 优化渲染完成: 0行 x 22列, 策略=empty_data, 耗时=0.0ms, 性能评级=excellent
2025-08-07 10:03:46.480 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:458 | 🚨 [UI数据修复] _update_visible_rows调用，数据行数: 0
2025-08-07 10:03:46.480 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 表格数据已设置: 0 行, 22 列
2025-08-07 10:03:46.482 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2870 | 表格数据设置完成: 0 行, 耗时: 8.0ms
2025-08-07 10:03:46.486 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:7661 | 🔧 [P0-排序修复] 开始恢复排序指示器显示
2025-08-07 10:03:46.486 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:7674 | 🔧 [P0-排序修复] 无排序状态需要恢复
2025-08-07 10:03:46.487 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1680 | 🔧 [列宽保存修复] 配置文件不存在: E:\project\case\salary_changes\salary_changes\state\column_widths.json，使用默认列宽
2025-08-07 10:03:46.487 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_fallback:2135 | 🔧 [P0-修复] 使用备用方案显示了标准空表格，表头数量: 22个
2025-08-07 10:03:46.492 | INFO     | src.gui.widgets.pagination_widget:_apply_styles:296 | 分页组件Material Design样式应用成功
2025-08-07 10:03:46.496 | INFO     | src.gui.widgets.pagination_widget:__init__:174 | ✅ [防抖升级] 智能防抖系统已启用
2025-08-07 10:03:46.497 | INFO     | src.gui.widgets.pagination_widget:__init__:182 | 分页组件初始化完成
2025-08-07 10:03:46.517 | INFO     | src.gui.prototype.prototype_main_window:_connect_control_panel_signals:526 | 控制面板按钮信号连接完成
2025-08-07 10:03:46.538 | INFO     | src.modules.system_config.config_manager:__init__:313 | 配置管理器初始化完成，配置文件: config.json
2025-08-07 10:03:46.538 | INFO     | src.modules.system_config.user_preferences:__init__:61 | 用户偏好设置管理器初始化完成，偏好文件: E:\project\case\salary_changes\salary_changes\user_preferences.json
2025-08-07 10:03:46.539 | INFO     | src.gui.prototype.prototype_main_window:_setup_shortcuts:4995 | 快捷键设置完成
2025-08-07 10:03:46.540 | INFO     | src.gui.prototype.prototype_main_window:_setup_ui:4984 | 主窗口UI设置完成。
2025-08-07 10:03:46.540 | INFO     | src.gui.prototype.prototype_main_window:_setup_connections:5181 | 🔧 [全局排序] 全局排序开关连接成功
2025-08-07 10:03:46.541 | INFO     | src.gui.prototype.prototype_main_window:_setup_connections:5213 | 🆕 [新架构排序] 使用表格组件内部的自定义排序循环，无需连接排序信号
2025-08-07 10:03:46.541 | INFO     | src.gui.prototype.prototype_main_window:_setup_connections:5222 | ✅ 已连接分页组件事件到新架构
2025-08-07 10:03:46.541 | INFO     | src.gui.prototype.prototype_main_window:_setup_connections:5224 | 信号连接设置完成
2025-08-07 10:03:46.542 | INFO     | src.gui.prototype.prototype_main_window:_load_field_mappings_from_file:6152 | 已加载字段映射信息，共0个表的映射
2025-08-07 10:03:46.546 | WARNING  | src.gui.prototype.prototype_main_window:set_data:723 | 尝试设置空数据，将显示提示信息。
2025-08-07 10:03:46.546 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:7398 | 🔧 [数据流追踪] 无当前表名，使用默认在职人员表头: 22个字段
2025-08-07 10:03:46.547 | WARNING  | src.gui.prototype.prototype_main_window:set_data:743 | 尝试设置空数据，将显示提示信息。
2025-08-07 10:03:46.547 | INFO     | src.gui.widgets.pagination_widget:reset:541 | 分页状态已重置
2025-08-07 10:03:46.547 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:7413 | 已显示标准空表格，表头数量: 22
2025-08-07 10:03:46.548 | INFO     | src.gui.widgets.pagination_widget:reset:541 | 分页状态已重置
2025-08-07 10:03:46.551 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:7398 | 🔧 [数据流追踪] 无当前表名，使用默认在职人员表头: 22个字段
2025-08-07 10:03:46.552 | WARNING  | src.gui.prototype.prototype_main_window:set_data:743 | 尝试设置空数据，将显示提示信息。
2025-08-07 10:03:46.552 | INFO     | src.gui.widgets.pagination_widget:reset:541 | 分页状态已重置
2025-08-07 10:03:46.553 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:7413 | 已显示标准空表格，表头数量: 22
2025-08-07 10:03:46.553 | INFO     | src.gui.prototype.prototype_main_window:__init__:3316 | 原型主窗口初始化完成
2025-08-07 10:03:46.608 | INFO     | __main__:main:440 | 应用程序启动成功
2025-08-07 10:03:46.612 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_ui_state_after_data_set:7696 | 🔧 [P0-2修复] 数据设置后UI状态恢复完成: default_table
2025-08-07 10:03:46.703 | INFO     | src.gui.prototype.prototype_main_window:_fix_display_brightness_after_data_refresh:8018 | 🔧 [P1-2] 开始智能显示亮度修复（增强防护）
2025-08-07 10:03:46.704 | INFO     | src.gui.prototype.prototype_main_window:_fix_display_brightness_after_data_refresh:8032 | 🔧 [P0-1] 检测到亮度问题: ['table_opacity_in_stylesheet', 'parent_opacity_effect']
2025-08-07 10:03:46.705 | INFO     | src.gui.prototype.prototype_main_window:_apply_brightness_fixes:8447 | 🔧 [P1-2] 开始应用增强版亮度修复: ['table_opacity_in_stylesheet', 'parent_opacity_effect']
2025-08-07 10:03:46.715 | INFO     | src.gui.prototype.prototype_main_window:_fix_display_brightness_after_data_refresh:8018 | 🔧 [P1-2] 开始智能显示亮度修复（增强防护）
2025-08-07 10:03:46.716 | INFO     | src.gui.prototype.managers.responsive_layout_manager:_apply_layout:181 | 断点切换: sm (宽度: 1280px)
2025-08-07 10:03:46.716 | INFO     | src.gui.prototype.prototype_main_window:handle_responsive_change:1953 | MainWorkspaceArea 响应式适配: sm
2025-08-07 10:03:46.727 | INFO     | src.gui.prototype.prototype_main_window:_fix_display_brightness_after_data_refresh:8038 | 🔧 [P0-1] 智能显示亮度修复完成
2025-08-07 10:03:46.776 | INFO     | src.gui.prototype.prototype_main_window:_fix_display_brightness_after_data_refresh:8018 | 🔧 [P1-2] 开始智能显示亮度修复（增强防护）
2025-08-07 10:03:47.129 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_save_column_widths:1659 | 🔧 [列宽保存修复] 准备保存列宽到文件: E:\project\case\salary_changes\salary_changes\state\column_widths.json
2025-08-07 10:03:47.129 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_save_column_widths:1660 | 🔧 [列宽保存修复] 表名: default_table, 列数: 22
2025-08-07 10:03:47.131 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_save_column_widths:1665 | 🔧 [列宽保存修复] 列宽设置保存成功！文件大小: 911 字节
2025-08-07 10:03:47.218 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_delayed_load_salary_data:1320 | 执行延迟的工资数据加载...
2025-08-07 10:03:47.219 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1601 | 开始强制刷新工资数据导航... (尝试 1/3)
2025-08-07 10:03:47.221 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1609 | 找到工资表节点: 💰 工资表
2025-08-07 10:03:47.227 | INFO     | src.modules.data_storage.dynamic_table_manager:get_table_list:885 | 找到 0 个匹配类型 'salary_data' 的表
2025-08-07 10:03:47.227 | WARNING  | src.modules.data_storage.dynamic_table_manager:get_navigation_tree_data:976 | 在 table_metadata 中未找到任何 'salary_data' 类型的表
2025-08-07 10:03:47.229 | INFO     | src.modules.data_storage.dynamic_table_manager:get_table_list:888 | 找到 3 个总表
2025-08-07 10:03:47.229 | WARNING  | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1661 | 检测到数据库中没有工资数据表，直接使用兜底数据
2025-08-07 10:03:47.230 | WARNING  | src.gui.prototype.widgets.enhanced_navigation_panel:_load_fallback_salary_data:1443 | 使用兜底数据加载导航
2025-08-07 10:03:47.237 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_delayed_auto_select_latest_data:1196 | 执行延迟的自动选择最新数据...
2025-08-07 10:03:47.238 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:auto_select_latest_data:1136 | 🔧 [P1-2修复] 开始自动选择最新数据...
2025-08-07 10:03:47.239 | INFO     | src.modules.data_storage.dynamic_table_manager:get_latest_salary_data_path:1036 | 开始获取最新工资数据路径...
2025-08-07 10:03:47.240 | INFO     | src.modules.data_storage.dynamic_table_manager:get_table_list:885 | 找到 0 个匹配类型 'salary_data' 的表
2025-08-07 10:03:47.241 | WARNING  | src.modules.data_storage.dynamic_table_manager:get_latest_salary_data_path:1041 | 未找到任何工资数据表
2025-08-07 10:03:47.242 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_get_latest_path_with_retry:1271 | 🔧 [P1-2修复] 检查表状态失败，1.0s后重试...
2025-08-07 10:03:48.243 | INFO     | src.modules.data_storage.dynamic_table_manager:get_latest_salary_data_path:1036 | 开始获取最新工资数据路径...
2025-08-07 10:03:48.244 | INFO     | src.modules.data_storage.dynamic_table_manager:get_table_list:885 | 找到 0 个匹配类型 'salary_data' 的表
2025-08-07 10:03:48.245 | WARNING  | src.modules.data_storage.dynamic_table_manager:get_latest_salary_data_path:1041 | 未找到任何工资数据表
2025-08-07 10:03:48.247 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_get_latest_path_with_retry:1271 | 🔧 [P1-2修复] 检查表状态失败，1.0s后重试...
2025-08-07 10:03:49.249 | INFO     | src.modules.data_storage.dynamic_table_manager:get_latest_salary_data_path:1036 | 开始获取最新工资数据路径...
2025-08-07 10:03:49.250 | INFO     | src.modules.data_storage.dynamic_table_manager:get_table_list:885 | 找到 0 个匹配类型 'salary_data' 的表
2025-08-07 10:03:49.252 | WARNING  | src.modules.data_storage.dynamic_table_manager:get_latest_salary_data_path:1041 | 未找到任何工资数据表
2025-08-07 10:03:49.253 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_get_latest_path_with_retry:1271 | 🔧 [P1-2修复] 检查表状态失败，1.0s后重试...
2025-08-07 10:03:50.254 | INFO     | src.modules.data_storage.dynamic_table_manager:get_latest_salary_data_path:1036 | 开始获取最新工资数据路径...
2025-08-07 10:03:50.256 | INFO     | src.modules.data_storage.dynamic_table_manager:get_table_list:885 | 找到 0 个匹配类型 'salary_data' 的表
2025-08-07 10:03:50.257 | WARNING  | src.modules.data_storage.dynamic_table_manager:get_latest_salary_data_path:1041 | 未找到任何工资数据表
2025-08-07 10:03:50.259 | WARNING  | src.gui.prototype.widgets.enhanced_navigation_panel:_get_latest_path_with_retry:1281 | 🔧 [P1-2修复] 4次尝试均失败，可能数据导入尚未完成
2025-08-07 10:03:50.260 | WARNING  | src.gui.prototype.widgets.enhanced_navigation_panel:auto_select_latest_data:1141 | 🔧 [P1-2修复] 多次重试后仍未找到最新工资数据路径
2025-08-07 10:03:50.261 | WARNING  | src.gui.prototype.widgets.enhanced_navigation_panel:_delayed_auto_select_latest_data:1216 | 延迟自动选择最新数据失败，可能没有可用数据
2025-08-07 10:03:50.262 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_post_refresh_auto_select:1349 | 导航树刷新完成，重新执行自动选择...
2025-08-07 10:03:50.263 | INFO     | src.modules.data_storage.dynamic_table_manager:get_latest_salary_data_path:1036 | 开始获取最新工资数据路径...
2025-08-07 10:03:50.264 | INFO     | src.modules.data_storage.dynamic_table_manager:get_table_list:885 | 找到 0 个匹配类型 'salary_data' 的表
2025-08-07 10:03:50.265 | WARNING  | src.modules.data_storage.dynamic_table_manager:get_latest_salary_data_path:1041 | 未找到任何工资数据表
2025-08-07 10:03:50.266 | WARNING  | src.gui.prototype.widgets.enhanced_navigation_panel:_post_refresh_auto_select:1354 | 未找到最新工资数据路径
2025-08-07 10:04:33.863 | INFO     | src.gui.prototype.prototype_main_window:_on_import_data:586 | 数据导入功能被触发，发出 import_requested 信号。
2025-08-07 10:04:33.864 | INFO     | src.gui.prototype.prototype_main_window:_on_import_data_requested:5455 | 接收到数据导入请求，推断的目标路径: 工资表 > 2025年 > 08月 > 全部在职人员。打开导入对话框。
2025-08-07 10:04:33.868 | INFO     | src.modules.data_import.auto_field_mapping_generator:__init__:60 | 自动字段映射生成器初始化完成
2025-08-07 10:04:33.869 | INFO     | src.modules.data_import.multi_sheet_importer:__init__:72 | 多Sheet导入器初始化完成
2025-08-07 10:04:33.870 | INFO     | src.gui.widgets.target_selection_widget:_save_config:285 | 配置保存成功
2025-08-07 10:04:33.880 | INFO     | src.gui.widgets.target_selection_widget:set_target_from_path:505 | 从路径设置目标: 工资表 > 2025年 > 08月 > 全部在职人员
2025-08-07 10:04:33.882 | INFO     | src.modules.data_storage.dynamic_table_manager:get_table_list:885 | 找到 0 个匹配类型 'salary_data' 的表
2025-08-07 10:04:33.903 | INFO     | src.gui.main_dialogs:_get_template_fields:1872 | 🔧 [P3-修复] ConfigSyncManager未初始化，使用默认字段模板
2025-08-07 10:04:33.904 | INFO     | src.gui.main_dialogs:_init_field_mapping:1859 | 🔧 [P3-修复] 未找到字段模板，使用内置默认字段列表
2025-08-07 10:04:33.927 | INFO     | src.modules.data_import.import_defaults_manager:load_settings:86 | 未找到用户设置文件，使用默认设置
2025-08-07 10:04:33.928 | INFO     | src.gui.main_dialogs:_apply_default_settings:2210 | 已应用默认设置: {'start_row': 1, 'import_mode': 'multi_sheet', 'auto_match_sheet': True, 'include_header': True, 'skip_empty_rows': True, 'create_table_mode': 'sheet_name', 'import_strategy': 'separate_tables', 'table_template': 'salary_data'}
2025-08-07 10:04:33.929 | INFO     | src.gui.main_dialogs:_setup_tooltips:2465 | 工具提示设置完成
2025-08-07 10:04:33.930 | INFO     | src.gui.main_dialogs:_setup_shortcuts:2504 | 快捷键设置完成
2025-08-07 10:04:33.930 | INFO     | src.gui.main_dialogs:__init__:77 | 数据导入对话框初始化完成。
2025-08-07 10:04:33.930 | INFO     | src.modules.data_import.multi_sheet_importer:set_config_sync_manager:81 | 🔧 [修复] ConfigSyncManager已设置到MultiSheetImporter
2025-08-07 10:04:33.931 | INFO     | src.gui.prototype.prototype_main_window:_on_import_data_requested:5466 | 🔧 [P0-修复] ConfigSyncManager已设置到数据导入对话框
2025-08-07 10:04:33.998 | INFO     | src.gui.prototype.prototype_main_window:_fix_display_brightness_after_data_refresh:8018 | 🔧 [P1-2] 开始智能显示亮度修复（增强防护）
2025-08-07 10:04:39.774 | INFO     | src.modules.data_import.excel_importer:get_sheet_names:173 | 检测到工作表: ['离休人员工资表', '退休人员工资表', '全部在职人员工资表', 'A岗职工']
2025-08-07 10:04:40.815 | INFO     | src.modules.data_import.excel_importer:get_sheet_names:173 | 检测到工作表: ['离休人员工资表', '退休人员工资表', '全部在职人员工资表', 'A岗职工']
2025-08-07 10:04:40.816 | INFO     | src.modules.data_import.smart_sheet_matcher:record_user_choice:372 | 记录用户选择: 全部在职人员 -> 全部在职人员工资表
2025-08-07 10:04:40.817 | INFO     | src.gui.main_dialogs:_auto_select_sheet_by_category:2245 | 根据人员类别 '全部在职人员' 自动选择工作表: 全部在职人员工资表 (匹配类型: fuzzy, 得分: 0.60)
2025-08-07 10:04:42.227 | INFO     | src.modules.data_import.excel_importer:get_sheet_names:173 | 检测到工作表: ['离休人员工资表', '退休人员工资表', '全部在职人员工资表', 'A岗职工']
2025-08-07 10:04:42.304 | INFO     | src.modules.data_import.excel_importer:get_sheet_names:173 | 检测到工作表: ['离休人员工资表', '退休人员工资表', '全部在职人员工资表', 'A岗职工']
2025-08-07 10:04:42.311 | INFO     | src.modules.data_import.excel_importer:validate_file:111 | 文件验证成功: 2025年5月份正式工工资（报财务)  最终版.xls (1.17MB)
2025-08-07 10:04:42.311 | INFO     | src.modules.data_import.multi_sheet_importer:import_excel_file:211 | 开始导入多Sheet Excel文件: 2025年5月份正式工工资（报财务)  最终版.xls
2025-08-07 10:04:42.312 | INFO     | src.modules.data_import.excel_importer:validate_file:111 | 文件验证成功: 2025年5月份正式工工资（报财务)  最终版.xls (1.17MB)
2025-08-07 10:04:42.388 | INFO     | src.modules.data_import.excel_importer:get_sheet_names:173 | 检测到工作表: ['离休人员工资表', '退休人员工资表', '全部在职人员工资表', 'A岗职工']
2025-08-07 10:04:42.388 | INFO     | src.modules.data_import.multi_sheet_importer:import_excel_file:222 | 检测到 4 个工作表: ['离休人员工资表', '退休人员工资表', '全部在职人员工资表', 'A岗职工']
2025-08-07 10:04:42.390 | INFO     | src.modules.data_import.excel_importer:validate_file:111 | 文件验证成功: 2025年5月份正式工工资（报财务)  最终版.xls (1.17MB)
2025-08-07 10:04:42.390 | INFO     | src.modules.data_import.excel_importer:import_data:260 | 开始导入Excel文件: 2025年5月份正式工工资（报财务)  最终版.xls
2025-08-07 10:04:42.390 | INFO     | src.utils.log_config:log_file_operation:274 | 文件E:\project\case\salary_changes\salary_changes\data\工资表\2025年5月份正式工工资（报财务)  最终版.xls: read
2025-08-07 10:04:42.431 | INFO     | src.modules.data_import.excel_importer:_import_normal_file:320 | 🔧 [修复标识] Excel读取完成: 16列 (列过滤: 否)
2025-08-07 10:04:42.432 | INFO     | src.modules.data_import.excel_importer:_clean_data:427 | 🔧 [修复标识] Excel导入器 _clean_data 方法 - 保留所有列结构修复已执行
2025-08-07 10:04:42.434 | INFO     | src.modules.data_import.excel_importer:_clean_data:438 | 数据清理完成: 保留所有 16 列 (原始 16 列)
2025-08-07 10:04:42.434 | INFO     | src.modules.data_import.excel_importer:_import_normal_file:339 | 导入完成: 3行 x 16列
2025-08-07 10:04:42.436 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:567 | 数据质量检查: 已过滤1条姓名为空的记录
2025-08-07 10:04:42.436 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:608 | 数据过滤完成: 原始3条记录，过滤1条无效记录，有效记录2条
2025-08-07 10:04:42.438 | INFO     | src.modules.data_import.excel_importer:import_data:286 | 🔧 [修复标识] 数据导入最终完成: 2行 × 16列
2025-08-07 10:04:42.438 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:456 | 工作表 离休人员工资表 使用智能默认配置
2025-08-07 10:04:42.439 | INFO     | src.modules.data_import.multi_sheet_importer:_generate_smart_default_config:744 | 为Sheet '离休人员工资表' 生成智能默认配置: 1 个必需字段
2025-08-07 10:04:42.439 | INFO     | src.modules.data_import.specialized_field_mapping_generator:generate_mapping:141 | 为模板 retired_employees 生成了 21 个字段映射
2025-08-07 10:04:42.439 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:473 | 使用专用模板 retired_employees 生成字段映射: 21 个字段
2025-08-07 10:04:42.441 | INFO     | src.modules.data_import.config_sync_manager:save_complete_mapping:655 | 🚀 [配置同步] 已发布配置变更事件: salary_data_2025_08_retired_employees
2025-08-07 10:04:42.445 | INFO     | src.modules.data_import.config_sync_manager:save_complete_mapping:659 | 完整字段映射保存成功: salary_data_2025_08_retired_employees
2025-08-07 10:04:42.445 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:520 | 为表 salary_data_2025_08_retired_employees 生成标准化字段映射: 21 个字段
2025-08-07 10:04:42.448 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:541 | Sheet 离休人员工资表 数据处理完成: 2 行
2025-08-07 10:04:42.448 | INFO     | src.modules.data_import.multi_sheet_importer:_import_separate_strategy:371 | Sheet '离休人员工资表' 检测到模板类型: retired_employees
2025-08-07 10:04:42.450 | INFO     | src.modules.data_storage.dynamic_table_manager:_create_table_from_schema:700 | 成功创建表: salary_data_2025_08_retired_employees
2025-08-07 10:04:42.451 | INFO     | src.modules.data_storage.dynamic_table_manager:create_specialized_salary_table:360 | 专用工资数据表创建成功: salary_data_2025_08_retired_employees (模板: retired_employees)
2025-08-07 10:04:42.471 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:1135 | [FIX] [修复标识] 导入字段映射加载完成: 28 个映射规则
2025-08-07 10:04:42.471 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:1143 | [FIX] [修复标识] 保留未映射列: ['data_source', 'import_time']
2025-08-07 10:04:42.472 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:1152 | [FIX] [修复标识] 导入列名映射成功: 18 个字段已映射
2025-08-07 10:04:42.476 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:1289 | 成功向表 salary_data_2025_08_retired_employees 保存 2 条数据。
2025-08-07 10:04:42.490 | INFO     | src.modules.data_import.excel_importer:validate_file:111 | 文件验证成功: 2025年5月份正式工工资（报财务)  最终版.xls (1.17MB)
2025-08-07 10:04:42.490 | INFO     | src.modules.data_import.excel_importer:import_data:260 | 开始导入Excel文件: 2025年5月份正式工工资（报财务)  最终版.xls
2025-08-07 10:04:42.491 | INFO     | src.utils.log_config:log_file_operation:274 | 文件E:\project\case\salary_changes\salary_changes\data\工资表\2025年5月份正式工工资（报财务)  最终版.xls: read
2025-08-07 10:04:42.532 | INFO     | src.modules.data_import.excel_importer:_import_normal_file:320 | 🔧 [修复标识] Excel读取完成: 27列 (列过滤: 否)
2025-08-07 10:04:42.533 | INFO     | src.modules.data_import.excel_importer:_clean_data:427 | 🔧 [修复标识] Excel导入器 _clean_data 方法 - 保留所有列结构修复已执行
2025-08-07 10:04:42.534 | INFO     | src.modules.data_import.excel_importer:_clean_data:438 | 数据清理完成: 保留所有 27 列 (原始 27 列)
2025-08-07 10:04:42.535 | INFO     | src.modules.data_import.excel_importer:_import_normal_file:339 | 导入完成: 14行 x 27列
2025-08-07 10:04:42.536 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:567 | 数据质量检查: 已过滤1条姓名为空的记录
2025-08-07 10:04:42.537 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:608 | 数据过滤完成: 原始14条记录，过滤1条无效记录，有效记录13条
2025-08-07 10:04:42.538 | INFO     | src.modules.data_import.excel_importer:import_data:286 | 🔧 [修复标识] 数据导入最终完成: 13行 × 27列
2025-08-07 10:04:42.538 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:456 | 工作表 退休人员工资表 使用智能默认配置
2025-08-07 10:04:42.538 | INFO     | src.modules.data_import.multi_sheet_importer:_generate_smart_default_config:744 | 为Sheet '退休人员工资表' 生成智能默认配置: 1 个必需字段
2025-08-07 10:04:42.539 | INFO     | src.modules.data_import.specialized_field_mapping_generator:generate_mapping:141 | 为模板 pension_employees 生成了 32 个字段映射
2025-08-07 10:04:42.539 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:473 | 使用专用模板 pension_employees 生成字段映射: 32 个字段
2025-08-07 10:04:42.541 | INFO     | src.modules.data_import.config_sync_manager:save_complete_mapping:655 | 🚀 [配置同步] 已发布配置变更事件: salary_data_2025_08_pension_employees
2025-08-07 10:04:42.544 | INFO     | src.modules.data_import.config_sync_manager:save_complete_mapping:659 | 完整字段映射保存成功: salary_data_2025_08_pension_employees
2025-08-07 10:04:42.544 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:520 | 为表 salary_data_2025_08_pension_employees 生成标准化字段映射: 32 个字段
2025-08-07 10:04:42.547 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:541 | Sheet 退休人员工资表 数据处理完成: 13 行
2025-08-07 10:04:42.547 | INFO     | src.modules.data_import.multi_sheet_importer:_import_separate_strategy:371 | Sheet '退休人员工资表' 检测到模板类型: pension_employees
2025-08-07 10:04:42.548 | INFO     | src.modules.data_storage.dynamic_table_manager:_create_table_from_schema:700 | 成功创建表: salary_data_2025_08_pension_employees
2025-08-07 10:04:42.549 | INFO     | src.modules.data_storage.dynamic_table_manager:create_specialized_salary_table:360 | 专用工资数据表创建成功: salary_data_2025_08_pension_employees (模板: pension_employees)
2025-08-07 10:04:42.561 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:1135 | [FIX] [修复标识] 导入字段映射加载完成: 32 个映射规则
2025-08-07 10:04:42.561 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:1143 | [FIX] [修复标识] 保留未映射列: ['data_source', 'import_time']
2025-08-07 10:04:42.562 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:1152 | [FIX] [修复标识] 导入列名映射成功: 29 个字段已映射
2025-08-07 10:04:42.567 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:1289 | 成功向表 salary_data_2025_08_pension_employees 保存 13 条数据。
2025-08-07 10:04:42.568 | INFO     | src.modules.data_import.excel_importer:validate_file:111 | 文件验证成功: 2025年5月份正式工工资（报财务)  最终版.xls (1.17MB)
2025-08-07 10:04:42.568 | INFO     | src.modules.data_import.excel_importer:import_data:260 | 开始导入Excel文件: 2025年5月份正式工工资（报财务)  最终版.xls
2025-08-07 10:04:42.568 | INFO     | src.utils.log_config:log_file_operation:274 | 文件E:\project\case\salary_changes\salary_changes\data\工资表\2025年5月份正式工工资（报财务)  最终版.xls: read
2025-08-07 10:04:42.617 | INFO     | src.modules.data_import.excel_importer:_import_normal_file:320 | 🔧 [修复标识] Excel读取完成: 23列 (列过滤: 否)
2025-08-07 10:04:42.619 | INFO     | src.modules.data_import.excel_importer:_clean_data:427 | 🔧 [修复标识] Excel导入器 _clean_data 方法 - 保留所有列结构修复已执行
2025-08-07 10:04:42.620 | INFO     | src.modules.data_import.excel_importer:_clean_data:438 | 数据清理完成: 保留所有 23 列 (原始 23 列)
2025-08-07 10:04:42.621 | INFO     | src.modules.data_import.excel_importer:_import_normal_file:339 | 导入完成: 1397行 x 23列
2025-08-07 10:04:42.623 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:567 | 数据质量检查: 已过滤1条姓名为空的记录
2025-08-07 10:04:42.624 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:608 | 数据过滤完成: 原始1397条记录，过滤1条无效记录，有效记录1396条
2025-08-07 10:04:42.625 | INFO     | src.modules.data_import.excel_importer:import_data:286 | 🔧 [修复标识] 数据导入最终完成: 1396行 × 23列
2025-08-07 10:04:42.626 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:456 | 工作表 全部在职人员工资表 使用智能默认配置
2025-08-07 10:04:42.626 | INFO     | src.modules.data_import.multi_sheet_importer:_generate_smart_default_config:744 | 为Sheet '全部在职人员工资表' 生成智能默认配置: 2 个必需字段
2025-08-07 10:04:42.627 | INFO     | src.modules.data_import.specialized_field_mapping_generator:generate_mapping:141 | 为模板 active_employees 生成了 28 个字段映射
2025-08-07 10:04:42.627 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:473 | 使用专用模板 active_employees 生成字段映射: 28 个字段
2025-08-07 10:04:42.629 | INFO     | src.modules.data_import.config_sync_manager:save_complete_mapping:655 | 🚀 [配置同步] 已发布配置变更事件: salary_data_2025_08_active_employees
2025-08-07 10:04:42.631 | INFO     | src.modules.data_import.config_sync_manager:save_complete_mapping:659 | 完整字段映射保存成功: salary_data_2025_08_active_employees
2025-08-07 10:04:42.631 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:520 | 为表 salary_data_2025_08_active_employees 生成标准化字段映射: 28 个字段
2025-08-07 10:04:42.635 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:541 | Sheet 全部在职人员工资表 数据处理完成: 1396 行
2025-08-07 10:04:42.635 | INFO     | src.modules.data_import.multi_sheet_importer:_import_separate_strategy:371 | Sheet '全部在职人员工资表' 检测到模板类型: active_employees
2025-08-07 10:04:42.636 | INFO     | src.modules.data_storage.dynamic_table_manager:_create_table_from_schema:700 | 成功创建表: salary_data_2025_08_active_employees
2025-08-07 10:04:42.638 | INFO     | src.modules.data_storage.dynamic_table_manager:create_specialized_salary_table:360 | 专用工资数据表创建成功: salary_data_2025_08_active_employees (模板: active_employees)
2025-08-07 10:04:42.649 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:1135 | [FIX] [修复标识] 导入字段映射加载完成: 28 个映射规则
2025-08-07 10:04:42.649 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:1143 | [FIX] [修复标识] 保留未映射列: ['data_source', 'import_time']
2025-08-07 10:04:42.650 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:1152 | [FIX] [修复标识] 导入列名映射成功: 25 个字段已映射
2025-08-07 10:04:42.662 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:1289 | 成功向表 salary_data_2025_08_active_employees 保存 1396 条数据。
2025-08-07 10:04:42.662 | INFO     | src.modules.data_import.excel_importer:validate_file:111 | 文件验证成功: 2025年5月份正式工工资（报财务)  最终版.xls (1.17MB)
2025-08-07 10:04:42.662 | INFO     | src.modules.data_import.excel_importer:import_data:260 | 开始导入Excel文件: 2025年5月份正式工工资（报财务)  最终版.xls
2025-08-07 10:04:42.663 | INFO     | src.utils.log_config:log_file_operation:274 | 文件E:\project\case\salary_changes\salary_changes\data\工资表\2025年5月份正式工工资（报财务)  最终版.xls: read
2025-08-07 10:04:42.702 | INFO     | src.modules.data_import.excel_importer:_import_normal_file:320 | 🔧 [修复标识] Excel读取完成: 21列 (列过滤: 否)
2025-08-07 10:04:42.703 | INFO     | src.modules.data_import.excel_importer:_clean_data:427 | 🔧 [修复标识] Excel导入器 _clean_data 方法 - 保留所有列结构修复已执行
2025-08-07 10:04:42.704 | INFO     | src.modules.data_import.excel_importer:_clean_data:438 | 数据清理完成: 保留所有 21 列 (原始 21 列)
2025-08-07 10:04:42.705 | INFO     | src.modules.data_import.excel_importer:_import_normal_file:339 | 导入完成: 63行 x 21列
2025-08-07 10:04:42.706 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:567 | 数据质量检查: 已过滤1条姓名为空的记录
2025-08-07 10:04:42.707 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:608 | 数据过滤完成: 原始63条记录，过滤1条无效记录，有效记录62条
2025-08-07 10:04:42.708 | INFO     | src.modules.data_import.excel_importer:import_data:286 | 🔧 [修复标识] 数据导入最终完成: 62行 × 21列
2025-08-07 10:04:42.708 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:456 | 工作表 A岗职工 使用智能默认配置
2025-08-07 10:04:42.709 | INFO     | src.modules.data_import.multi_sheet_importer:_generate_smart_default_config:744 | 为Sheet 'A岗职工' 生成智能默认配置: 2 个必需字段
2025-08-07 10:04:42.709 | INFO     | src.modules.data_import.specialized_field_mapping_generator:generate_mapping:141 | 为模板 a_grade_employees 生成了 26 个字段映射
2025-08-07 10:04:42.709 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:473 | 使用专用模板 a_grade_employees 生成字段映射: 26 个字段
2025-08-07 10:04:42.711 | INFO     | src.modules.data_import.config_sync_manager:save_complete_mapping:655 | 🚀 [配置同步] 已发布配置变更事件: salary_data_2025_08_a_grade_employees
2025-08-07 10:04:42.712 | INFO     | src.modules.data_import.config_sync_manager:save_complete_mapping:659 | 完整字段映射保存成功: salary_data_2025_08_a_grade_employees
2025-08-07 10:04:42.715 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:520 | 为表 salary_data_2025_08_a_grade_employees 生成标准化字段映射: 26 个字段
2025-08-07 10:04:42.718 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:541 | Sheet A岗职工 数据处理完成: 62 行
2025-08-07 10:04:42.718 | INFO     | src.modules.data_import.multi_sheet_importer:_import_separate_strategy:371 | Sheet 'A岗职工' 检测到模板类型: a_grade_employees
2025-08-07 10:04:42.719 | INFO     | src.modules.data_storage.dynamic_table_manager:_create_table_from_schema:700 | 成功创建表: salary_data_2025_08_a_grade_employees
2025-08-07 10:04:42.720 | INFO     | src.modules.data_storage.dynamic_table_manager:create_specialized_salary_table:360 | 专用工资数据表创建成功: salary_data_2025_08_a_grade_employees (模板: a_grade_employees)
2025-08-07 10:04:42.731 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:1135 | [FIX] [修复标识] 导入字段映射加载完成: 26 个映射规则
2025-08-07 10:04:42.732 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:1143 | [FIX] [修复标识] 保留未映射列: ['data_source', 'import_time']
2025-08-07 10:04:42.732 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:1152 | [FIX] [修复标识] 导入列名映射成功: 23 个字段已映射
2025-08-07 10:04:42.736 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:1289 | 成功向表 salary_data_2025_08_a_grade_employees 保存 62 条数据。
2025-08-07 10:04:42.737 | INFO     | src.modules.data_import.multi_sheet_importer:import_excel_file:241 | 多Sheet导入完成: {'success': True, 'results': {'离休人员工资表': {'success': True, 'message': '成功向表 salary_data_2025_08_retired_employees 保存 2 条数据。', 'table_name': 'salary_data_2025_08_retired_employees', 'records': 2}, '退休人员工资表': {'success': True, 'message': '成功向表 salary_data_2025_08_pension_employees 保存 13 条数据。', 'table_name': 'salary_data_2025_08_pension_employees', 'records': 13}, '全部在职人员工资表': {'success': True, 'message': '成功向表 salary_data_2025_08_active_employees 保存 1396 条数据。', 'table_name': 'salary_data_2025_08_active_employees', 'records': 1396}, 'A岗职工': {'success': True, 'message': '成功向表 salary_data_2025_08_a_grade_employees 保存 62 条数据。', 'table_name': 'salary_data_2025_08_a_grade_employees', 'records': 62}}, 'total_records': 1473, 'import_stats': {'total_sheets': 4, 'processed_sheets': 4, 'total_records': 1473, 'failed_records': 0, 'validation_errors': 0}, 'file_info': {'path': 'E:\\project\\case\\salary_changes\\salary_changes\\data\\工资表\\2025年5月份正式工工资（报财务)  最终版.xls', 'name': '2025年5月份正式工工资（报财务)  最终版.xls', 'size_mb': 1.1650390625, 'extension': '.xls', 'is_valid': True, 'error_message': None}, 'processed_sheets': ['离休人员工资表', '退休人员工资表', '全部在职人员工资表', 'A岗职工']}
2025-08-07 10:04:42.737 | INFO     | src.gui.main_dialogs:_execute_multi_sheet_import:1479 | 多Sheet导入成功: {'success': True, 'results': {'离休人员工资表': {'success': True, 'message': '成功向表 salary_data_2025_08_retired_employees 保存 2 条数据。', 'table_name': 'salary_data_2025_08_retired_employees', 'records': 2}, '退休人员工资表': {'success': True, 'message': '成功向表 salary_data_2025_08_pension_employees 保存 13 条数据。', 'table_name': 'salary_data_2025_08_pension_employees', 'records': 13}, '全部在职人员工资表': {'success': True, 'message': '成功向表 salary_data_2025_08_active_employees 保存 1396 条数据。', 'table_name': 'salary_data_2025_08_active_employees', 'records': 1396}, 'A岗职工': {'success': True, 'message': '成功向表 salary_data_2025_08_a_grade_employees 保存 62 条数据。', 'table_name': 'salary_data_2025_08_a_grade_employees', 'records': 62}}, 'total_records': 1473, 'import_stats': {'total_sheets': 4, 'processed_sheets': 4, 'total_records': 1473, 'failed_records': 0, 'validation_errors': 0}, 'file_info': {'path': 'E:\\project\\case\\salary_changes\\salary_changes\\data\\工资表\\2025年5月份正式工工资（报财务)  最终版.xls', 'name': '2025年5月份正式工工资（报财务)  最终版.xls', 'size_mb': 1.1650390625, 'extension': '.xls', 'is_valid': True, 'error_message': None}, 'processed_sheets': ['离休人员工资表', '退休人员工资表', '全部在职人员工资表', 'A岗职工'], 'import_mode': 'multi_sheet', 'strategy': 'separate_tables', 'data_period': '2025-08', 'data_description': '2025年8月工资数据', 'file_path': 'E:/project/case/salary_changes/salary_changes/data/工资表/2025年5月份正式工工资（报财务)  最终版.xls', 'target_path': '工资表 > 2025年 > 08月 > 全部在职人员'}
2025-08-07 10:04:42.740 | INFO     | src.gui.prototype.prototype_main_window:_handle_data_imported:5485 | 开始处理导入结果: {'success': True, 'results': {'离休人员工资表': {'success': True, 'message': '成功向表 salary_data_2025_08_retired_employees 保存 2 条数据。', 'table_name': 'salary_data_2025_08_retired_employees', 'records': 2}, '退休人员工资表': {'success': True, 'message': '成功向表 salary_data_2025_08_pension_employees 保存 13 条数据。', 'table_name': 'salary_data_2025_08_pension_employees', 'records': 13}, '全部在职人员工资表': {'success': True, 'message': '成功向表 salary_data_2025_08_active_employees 保存 1396 条数据。', 'table_name': 'salary_data_2025_08_active_employees', 'records': 1396}, 'A岗职工': {'success': True, 'message': '成功向表 salary_data_2025_08_a_grade_employees 保存 62 条数据。', 'table_name': 'salary_data_2025_08_a_grade_employees', 'records': 62}}, 'total_records': 1473, 'import_stats': {'total_sheets': 4, 'processed_sheets': 4, 'total_records': 1473, 'failed_records': 0, 'validation_errors': 0}, 'file_info': {'path': 'E:\\project\\case\\salary_changes\\salary_changes\\data\\工资表\\2025年5月份正式工工资（报财务)  最终版.xls', 'name': '2025年5月份正式工工资（报财务)  最终版.xls', 'size_mb': 1.1650390625, 'extension': '.xls', 'is_valid': True, 'error_message': None}, 'processed_sheets': ['离休人员工资表', '退休人员工资表', '全部在职人员工资表', 'A岗职工'], 'import_mode': 'multi_sheet', 'strategy': 'separate_tables', 'data_period': '2025-08', 'data_description': '2025年8月工资数据', 'file_path': 'E:/project/case/salary_changes/salary_changes/data/工资表/2025年5月份正式工工资（报财务)  最终版.xls', 'target_path': '工资表 > 2025年 > 08月 > 全部在职人员'}
2025-08-07 10:04:42.741 | INFO     | src.gui.prototype.prototype_main_window:_handle_data_imported:5496 | 导入模式: multi_sheet, 目标路径: '工资表 > 2025年 > 08月 > 全部在职人员'
2025-08-07 10:04:42.741 | INFO     | src.gui.prototype.prototype_main_window:_handle_data_imported:5514 | 接收到导入数据, 来源: 未知来源, 目标路径: 工资表 > 2025年 > 08月 > 全部在职人员
2025-08-07 10:04:43.543 | INFO     | src.gui.prototype.prototype_main_window:_update_navigation_if_needed:5602 | 检查是否需要更新导航面板: ['工资表', '2025年', '08月', '全部在职人员']
2025-08-07 10:04:43.543 | INFO     | src.gui.prototype.prototype_main_window:_update_navigation_if_needed:5606 | 检测到工资数据导入，开始刷新导航面板
2025-08-07 10:04:43.546 | INFO     | src.gui.prototype.prototype_main_window:_update_navigation_if_needed:5610 | 使用强制刷新方法
2025-08-07 10:04:43.547 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1601 | 开始强制刷新工资数据导航... (尝试 1/3)
2025-08-07 10:04:43.548 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1609 | 找到工资表节点: 💰 工资表
2025-08-07 10:04:43.554 | INFO     | src.modules.data_storage.dynamic_table_manager:get_table_list:885 | 找到 4 个匹配类型 'salary_data' 的表
2025-08-07 10:04:43.557 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1694 | 创建年份节点: 2025年，包含 1 个月份
2025-08-07 10:04:43.558 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1714 | 工资数据导航已强制刷新: 1 个年份, 1 个月份
2025-08-07 10:04:43.558 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1717 | 默认路径: 工资表 > 2025年 > 8月 > 全部在职人员
2025-08-07 10:04:43.559 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1756 | force_refresh_salary_data 执行完成
2025-08-07 10:04:43.560 | INFO     | src.gui.prototype.prototype_main_window:_update_navigation_if_needed:5615 | 将在1500ms后导航到: 工资表 > 2025年 > 08月 > 全部在职人员
2025-08-07 10:04:43.561 | INFO     | src.gui.prototype.prototype_main_window:_navigate_to_imported_path:5682 | 尝试导航到新导入的路径: 工资表 > 2025年 > 08月 > 全部在职人员
2025-08-07 10:04:43.562 | INFO     | src.gui.prototype.prototype_main_window:_navigate_to_imported_path:5687 | 已成功导航到新导入的路径: 工资表 > 2025年 > 08月 > 全部在职人员
2025-08-07 10:04:43.563 | INFO     | src.gui.prototype.prototype_main_window:_generate_table_name_from_path:7231 | 🔧 [表名生成] 从完整路径生成表名: ['工资表', '2025年', '08月', '全部在职人员'] -> salary_data_2025_08_active_employees
2025-08-07 10:04:43.564 | INFO     | src.gui.prototype.prototype_main_window:_refresh_current_data_display:5775 | [数据流追踪] 开始智能数据显示刷新: salary_data_2025_08_active_employees
2025-08-07 10:04:43.575 | INFO     | src.core.smart_pagination_strategy:__init__:47 | 智能分页策略管理器初始化完成
2025-08-07 10:04:43.576 | INFO     | src.core.smart_pagination_strategy:should_use_pagination:68 | [数据流追踪] 分页决策输入: 总记录数=1396, 页面大小=50, 用户偏好=None
2025-08-07 10:04:43.576 | INFO     | src.core.smart_pagination_strategy:should_use_pagination:93 | [数据流追踪] 分页决策输出: 策略=pagination, 原因=大数据集(1396条)分28页显示, 预期性能=550ms, 决策耗时=0ms
2025-08-07 10:04:43.577 | INFO     | src.gui.prototype.prototype_main_window:_refresh_current_data_display:5797 | [数据流追踪] 智能分页策略决策: pagination, 原因=大数据集(1396条)分28页显示, 预期性能=550ms
2025-08-07 10:04:43.581 | INFO     | src.gui.prototype.prototype_main_window:_execute_pagination_mode:5871 | [数据流追踪] 执行分页显示模式: salary_data_2025_08_active_employees, 1396条记录
2025-08-07 10:04:43.582 | INFO     | src.gui.prototype.prototype_main_window:_load_data_with_pagination:6732 | 使用分页模式加载 salary_data_2025_08_active_employees，第1页，每页50条
2025-08-07 10:04:43.584 | INFO     | src.gui.prototype.prototype_main_window:_load_data_with_pagination:6819 | [数据流追踪] 优化缓存模块未找到: No module named 'cache_optimization_fix'，使用标准缓存机制
2025-08-07 10:04:43.585 | INFO     | src.gui.prototype.prototype_main_window:_load_data_with_pagination:6861 | 缓存未命中，从数据库加载: salary_data_2025_08_active_employees 第1页
2025-08-07 10:04:43.587 | INFO     | src.gui.prototype.prototype_main_window:_execute_pagination_mode:5889 | [数据流追踪] 分页显示模式: 1396条记录分28页显示
2025-08-07 10:04:43.587 | INFO     | src.gui.prototype.prototype_main_window:run:133 | 开始加载表 salary_data_2025_08_active_employees 第1页数据，每页50条
2025-08-07 10:04:43.588 | INFO     | src.gui.prototype.prototype_main_window:_schedule_safe_navigation:5660 | 同步导航完成: 工资表 > 2025年 > 08月 > 全部在职人员
2025-08-07 10:04:43.603 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:487 | 正在从表 salary_data_2025_08_active_employees 分页获取数据（支持排序）: 第1页, 每页50条, 排序=0列
2025-08-07 10:04:43.604 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:547 | [P0-CRITICAL修复] 执行的完整SQL查询: SELECT * FROM "salary_data_2025_08_active_employees" LIMIT 50 OFFSET 0
2025-08-07 10:04:43.610 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:557 | [P0-CRITICAL修复] 数据库查询返回的前5个工号: ['19990089.0', '20161565.0', '20191782.0', '20151515.0', '20181640.0']
2025-08-07 10:04:43.612 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:569 | 成功从表 salary_data_2025_08_active_employees 获取第1页数据（含排序）: 50 行，总计1396行
2025-08-07 10:04:43.613 | INFO     | src.gui.prototype.prototype_main_window:run:159 | 使用排序查询: 0 个排序列
2025-08-07 10:04:43.614 | INFO     | src.gui.prototype.prototype_main_window:run:190 | 原始数据: 50行, 28列
2025-08-07 10:04:43.618 | INFO     | src.gui.prototype.prototype_main_window:run:197 | 开始应用字段映射
2025-08-07 10:04:43.619 | INFO     | src.gui.prototype.prototype_main_window:_apply_unified_field_processing:6296 | 开始统一字段处理: salary_data_2025_08_active_employees, 原始列数: 28
2025-08-07 10:04:43.661 | INFO     | src.modules.format_management.field_registry:__init__:95 | 🏷️ [字段注册] 字段注册系统初始化: state/data/field_mappings.json
2025-08-07 10:04:43.662 | INFO     | src.modules.format_management.format_renderer:__init__:83 | 🎨 [格式渲染] 格式渲染器初始化完成
2025-08-07 10:04:43.663 | INFO     | src.modules.format_management.unified_format_manager:_setup_event_listeners:877 | 🎯 [事件驱动] 事件监听器设置完成
2025-08-07 10:04:43.664 | INFO     | src.modules.format_management.unified_format_manager:_sync_to_state_manager:892 | 🎯 [统一状态管理] 状态同步完成
2025-08-07 10:04:43.665 | INFO     | src.modules.format_management.format_config:load_config:385 | 🔧 [格式配置] 配置文件不存在，创建默认配置
2025-08-07 10:04:43.666 | INFO     | src.modules.format_management.format_config:save_config:435 | 🔧 [格式配置] 配置文件保存成功
2025-08-07 10:04:43.666 | INFO     | src.modules.format_management.field_registry:_merge_default_mappings:736 | 🔧 [自动修复] 为表 salary_data_2025_07_active_employees 自动生成字段类型配置
2025-08-07 10:04:43.667 | INFO     | src.modules.format_management.field_registry:_find_matching_default_config:920 | 🔧 [智能匹配] 表 salary_data_2025_07_active_employees 匹配成功: active_employees (得分:220, 原因:精确后缀匹配:active_employees,英文关键词:active,模式匹配:.*active.*employee.*,分词匹配:active)
2025-08-07 10:04:43.668 | INFO     | src.modules.format_management.field_registry:_merge_default_mappings:754 | 🔧 [自动修复] 使用匹配的默认配置: salary_data_2025_07_active_employees
2025-08-07 10:04:43.668 | INFO     | src.modules.format_management.field_registry:_merge_default_mappings:736 | 🔧 [自动修复] 为表 salary_data_2025_08_retired_employees 自动生成字段类型配置
2025-08-07 10:04:43.668 | INFO     | src.modules.format_management.field_registry:_find_matching_default_config:920 | 🔧 [智能匹配] 表 salary_data_2025_08_retired_employees 匹配成功: retired_employees (得分:220, 原因:精确后缀匹配:retired_employees,英文关键词:retired,模式匹配:.*retired.*employee.*,分词匹配:retired)
2025-08-07 10:04:43.669 | INFO     | src.modules.format_management.field_registry:_merge_default_mappings:754 | 🔧 [自动修复] 使用匹配的默认配置: salary_data_2025_08_retired_employees
2025-08-07 10:04:43.669 | INFO     | src.modules.format_management.field_registry:_merge_default_mappings:736 | 🔧 [自动修复] 为表 salary_data_2025_08_pension_employees 自动生成字段类型配置
2025-08-07 10:04:43.670 | INFO     | src.modules.format_management.field_registry:_find_matching_default_config:920 | 🔧 [智能匹配] 表 salary_data_2025_08_pension_employees 匹配成功: pension_employees (得分:300, 原因:精确后缀匹配:pension_employees,英文关键词:pension,英文关键词:pension_employees,模式匹配:.*pension.*employee.*,模式匹配:.*pension.*,分词匹配:pension)
2025-08-07 10:04:43.670 | INFO     | src.modules.format_management.field_registry:_merge_default_mappings:754 | 🔧 [自动修复] 使用匹配的默认配置: salary_data_2025_08_pension_employees
2025-08-07 10:04:43.671 | INFO     | src.modules.format_management.field_registry:_merge_default_mappings:736 | 🔧 [自动修复] 为表 salary_data_2025_08_active_employees 自动生成字段类型配置
2025-08-07 10:04:43.671 | INFO     | src.modules.format_management.field_registry:_find_matching_default_config:920 | 🔧 [智能匹配] 表 salary_data_2025_08_active_employees 匹配成功: active_employees (得分:220, 原因:精确后缀匹配:active_employees,英文关键词:active,模式匹配:.*active.*employee.*,分词匹配:active)
2025-08-07 10:04:43.672 | INFO     | src.modules.format_management.field_registry:_merge_default_mappings:754 | 🔧 [自动修复] 使用匹配的默认配置: salary_data_2025_08_active_employees
2025-08-07 10:04:43.676 | INFO     | src.modules.format_management.field_registry:_merge_default_mappings:736 | 🔧 [自动修复] 为表 salary_data_2025_08_a_grade_employees 自动生成字段类型配置
2025-08-07 10:04:43.676 | INFO     | src.modules.format_management.field_registry:_find_matching_default_config:920 | 🔧 [智能匹配] 表 salary_data_2025_08_a_grade_employees 匹配成功: a_grade_employees (得分:330, 原因:精确后缀匹配:a_grade_employees,英文关键词:a_grade,英文关键词:a_grade_employees,模式匹配:.*a.*grade.*employee.*,优先级加分:100)
2025-08-07 10:04:43.677 | INFO     | src.modules.format_management.field_registry:_merge_default_mappings:754 | 🔧 [自动修复] 使用匹配的默认配置: salary_data_2025_08_a_grade_employees
2025-08-07 10:04:43.678 | WARNING  | src.modules.format_management.field_registry:_merge_default_mappings:777 | 🔧 [架构优化] 配置一致性警告: ["表 salary_data_2025_08_active_employees 有字段缺少类型定义: {'sequence_number'}", "表 salary_data_2025_08_active_employees 有多余的字段类型定义: {'row_number', 'sequence'}", '🔧 [P3增强] 表 part_time_employees 字段 total_salary 可能应该是float类型，当前: currency', '🔧 [P3增强] 表 contract_employees 字段 base_salary 可能应该是float类型，当前: currency', '🔧 [P3增强] 表 contract_employees 字段 performance_bonus 可能应该是float类型，当前: currency', '🔧 [P3增强] 表 contract_employees 字段 allowance 可能应该是float类型，当前: currency', '🔧 [P3增强] 表 contract_employees 字段 total_salary 可能应该是float类型，当前: currency', '🔧 [P3增强] 表 contract_employees 字段 social_insurance 可能应该是float类型，当前: currency', '🔧 [P3增强] 表 salary_data_2025_07_active_employees existing_display_fields为空，将导致FormatRenderer降级处理', '🔧 [P3增强] 表 active_employees existing_display_fields为空，将导致FormatRenderer降级处理', '🔧 [P3增强] 表 salary_data_2025_08_retired_employees existing_display_fields为空，将导致FormatRenderer降级处理', '🔧 [P3增强] 表 salary_data_2025_08_pension_employees existing_display_fields为空，将导致FormatRenderer降级处理', '🔧 [P3增强] 表 salary_data_2025_08_active_employees existing_display_fields为空，将导致FormatRenderer降级处理', '🔧 [P3增强] 表 salary_data_2025_08_a_grade_employees existing_display_fields为空，将导致FormatRenderer降级处理', '🔧 [P3增强] 表 retired_employees existing_display_fields为空，将导致FormatRenderer降级处理', '🔧 [P3增强] 表 pension_employees existing_display_fields为空，将导致FormatRenderer降级处理', '🔧 [P3增强] 表 part_time_employees existing_display_fields为空，将导致FormatRenderer降级处理', '🔧 [P3增强] 表 contract_employees existing_display_fields为空，将导致FormatRenderer降级处理', '🔧 [P3增强] 表 a_grade_employees existing_display_fields为空，将导致FormatRenderer降级处理']
2025-08-07 10:04:43.678 | INFO     | src.modules.format_management.field_registry:_apply_smart_config_repair_strategy:2007 | 🔧 [P1-1修复] 表 active_employees existing_display_fields已自动修复: ['employee_id', 'employee_name', 'department', 'employee_type', 'employee_type_code']...
2025-08-07 10:04:43.679 | INFO     | src.modules.format_management.field_registry:_apply_smart_config_repair_strategy:2007 | 🔧 [P1-1修复] 表 salary_data_2025_08_retired_employees existing_display_fields已自动修复: ['employee_id', 'employee_name', 'department', 'basic_retirement_salary', 'balance_allowance']...
2025-08-07 10:04:43.679 | INFO     | src.modules.format_management.field_registry:_apply_smart_config_repair_strategy:2007 | 🔧 [P1-1修复] 表 salary_data_2025_08_pension_employees existing_display_fields已自动修复: ['employee_id', 'employee_name', 'department', 'employee_type_code', 'basic_retirement_salary']...
2025-08-07 10:04:43.680 | INFO     | src.modules.format_management.field_registry:_apply_smart_config_repair_strategy:2007 | 🔧 [P1-1修复] 表 salary_data_2025_08_active_employees existing_display_fields已自动修复: ['employee_id', 'employee_name', 'department', 'employee_type', 'employee_type_code']...
2025-08-07 10:04:43.680 | INFO     | src.modules.format_management.field_registry:_apply_smart_config_repair_strategy:2007 | 🔧 [P1-1修复] 表 salary_data_2025_08_a_grade_employees existing_display_fields已自动修复: ['employee_id', 'employee_name', 'department', 'employee_type', 'employee_type_code']...
2025-08-07 10:04:43.681 | INFO     | src.modules.format_management.field_registry:_apply_smart_config_repair_strategy:2007 | 🔧 [P1-1修复] 表 retired_employees existing_display_fields已自动修复: ['employee_id', 'employee_name', 'department', 'basic_retirement_salary', 'balance_allowance']...
2025-08-07 10:04:43.681 | INFO     | src.modules.format_management.field_registry:_apply_smart_config_repair_strategy:2007 | 🔧 [P1-1修复] 表 pension_employees existing_display_fields已自动修复: ['employee_id', 'employee_name', 'department', 'employee_type_code', 'basic_retirement_salary']...
2025-08-07 10:04:43.682 | INFO     | src.modules.format_management.field_registry:_apply_smart_config_repair_strategy:2007 | 🔧 [P1-1修复] 表 part_time_employees existing_display_fields已自动修复: ['employee_id', 'employee_name', 'department', 'hourly_rate', 'hours_worked']...
2025-08-07 10:04:43.682 | INFO     | src.modules.format_management.field_registry:_apply_smart_config_repair_strategy:2007 | 🔧 [P1-1修复] 表 contract_employees existing_display_fields已自动修复: ['employee_id', 'employee_name', 'department', 'base_salary', 'performance_bonus']...
2025-08-07 10:04:43.682 | INFO     | src.modules.format_management.field_registry:_apply_smart_config_repair_strategy:2007 | 🔧 [P1-1修复] 表 a_grade_employees existing_display_fields已自动修复: ['employee_id', 'employee_name', 'department', 'employee_type', 'employee_type_code']...
2025-08-07 10:04:43.683 | INFO     | src.modules.format_management.field_registry:_apply_smart_config_repair_strategy:2017 | 🔧 [架构优化] 应用智能修复策略，执行 12 项修复操作
2025-08-07 10:04:43.685 | INFO     | src.modules.format_management.field_registry:save_mappings:661 | 🏷️ [字段注册] 字段映射保存成功
2025-08-07 10:04:43.688 | INFO     | src.modules.format_management.field_registry:_merge_default_mappings:785 | 🔧 [架构优化] 配置修复已持久化保存，变更数量: 5
2025-08-07 10:04:43.688 | INFO     | src.modules.format_management.field_registry:load_mappings:628 | 🏷️ [字段注册] 字段映射加载成功
2025-08-07 10:04:43.689 | INFO     | src.modules.format_management.unified_format_manager:_initialize_system:146 | 🎯 [统一格式管理] 系统初始化完成
2025-08-07 10:04:43.689 | INFO     | src.modules.format_management.unified_format_manager:__init__:130 | 🎯 [统一格式管理] 统一格式管理器初始化完成（新架构版本）
2025-08-07 10:04:43.690 | INFO     | src.core.architecture_factory:get_unified_format_manager:293 | 🎯 [统一格式管理] 统一格式管理器创建成功
2025-08-07 10:04:43.690 | INFO     | src.modules.format_management.format_renderer:render_dataframe:107 | 🎯 [格式渲染] 隐藏字段配置: ['created_at', 'id', 'updated_at', 'sequence', 'row_number', 'sequence_number']
2025-08-07 10:04:43.691 | INFO     | src.modules.format_management.format_renderer:render_dataframe:113 | 🎯 [格式渲染] 已隐藏字段: ['created_at', 'id', 'updated_at', 'sequence_number']
2025-08-07 10:04:43.698 | INFO     | src.modules.format_management.field_registry:get_display_fields:1553 | 🔧 [P0-2修复] display_order转换完成: 24个字段，原始字段数: 24
2025-08-07 10:04:43.699 | INFO     | src.modules.format_management.format_renderer:render_dataframe:153 | 🔧 [DEBUG] table_type=salary_data_2025_08_active_employees, display_fields=24个字段
2025-08-07 10:04:43.699 | INFO     | src.modules.format_management.format_renderer:render_dataframe:174 | 🔧 [DEBUG] formatted_df.columns=24个, existing_display_fields=0个
2025-08-07 10:04:43.700 | WARNING  | src.modules.format_management.format_renderer:render_dataframe:181 | 🔧 [格式修复] existing_display_fields为空，使用所有列作为降级处理
2025-08-07 10:04:43.700 | INFO     | src.modules.format_management.format_renderer:render_dataframe:183 | 🔧 [格式修复] 保持原始列顺序: 24列
2025-08-07 10:04:43.700 | INFO     | src.modules.format_management.format_renderer:render_dataframe:199 | 🎨 [格式渲染] DataFrame格式化完成: salary_data_2025_08_active_employees, 行数: 50, 列数: 24
2025-08-07 10:04:43.701 | INFO     | src.modules.format_management.unified_format_manager:format_data:363 | 🎯 [统一格式管理] 数据格式化完成: salary_data_2025_08_active_employees, 行数: 50, 列数: 24
2025-08-07 10:04:43.701 | INFO     | src.gui.prototype.prototype_main_window:_apply_unified_field_processing:6304 | 🔧 [调试] 格式化时删除的列: {'created_at', 'id', 'updated_at', 'sequence_number'}
2025-08-07 10:04:43.711 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:6501 | 表 salary_data_2025_08_active_employees 没有用户偏好设置，显示所有可见字段
2025-08-07 10:04:43.712 | INFO     | src.gui.prototype.prototype_main_window:_apply_unified_field_processing:6357 | 🔧 [字段处理] 统一字段处理完成并缓存: 24个字段
2025-08-07 10:04:43.712 | INFO     | src.gui.prototype.prototype_main_window:run:207 | PaginationWorker - 字段映射成功: 28 -> 24列
2025-08-07 10:04:43.713 | INFO     | src.gui.prototype.prototype_main_window:run:221 | 字段映射成功: 24列
2025-08-07 10:04:43.714 | INFO     | src.gui.prototype.prototype_main_window:run:247 | 最终数据: 50行, 24列, 总记录数: 1396
2025-08-07 10:04:43.714 | INFO     | src.gui.prototype.prototype_main_window:_on_pagination_data_loaded:6892 | 分页数据加载成功（数据库）: 50条数据，第1页，总计1396条
2025-08-07 10:04:43.715 | INFO     | src.gui.prototype.prototype_main_window:_on_pagination_data_loaded:6926 | 🚀 [性能缓存] 数据已缓存: salary_data_2025_08_active_employees 第1页
2025-08-07 10:04:43.717 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:439 | 正在从表 salary_data_2025_08_active_employees 分页获取数据: 第2页, 每页50条
2025-08-07 10:04:43.718 | INFO     | src.gui.prototype.prototype_main_window:_on_pagination_data_loaded:6955 | 🧹 [异步分页] 使用重构后的统一格式化结果: 50行, 24列
2025-08-07 10:04:43.720 | INFO     | src.gui.prototype.prototype_main_window:set_data:763 | 通过公共接口设置新的表格数据(DataFrame): 50 行
2025-08-07 10:04:43.721 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:462 | 成功从表 salary_data_2025_08_active_employees 获取第2页数据: 50 行，总计1396行
2025-08-07 10:04:43.724 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:6501 | 表 salary_data_2025_08_active_employees 没有用户偏好设置，显示所有可见字段
2025-08-07 10:04:43.727 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2612 | 🚨 [UI数据修复] 保存到original_data第0行: 工号=19990089, 薪资=2375.00
2025-08-07 10:04:43.727 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2612 | 🚨 [UI数据修复] 保存到original_data第1行: 工号=20161565, 薪资=1696.00
2025-08-07 10:04:43.728 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:5011 | 最大可见行数已更新: 1000 -> 50
2025-08-07 10:04:43.728 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:5064 | 根据数据量(50)自动调整最大可见行数为: 50
2025-08-07 10:04:43.732 | INFO     | src.core.event_bus:__init__:227 | 事件总线初始化完成
2025-08-07 10:04:43.738 | INFO     | src.core.unified_state_manager:_load_state:579 | 状态已从文件加载: state/unified_state.json
2025-08-07 10:04:43.738 | INFO     | src.core.unified_state_manager:__init__:181 | 统一状态管理器初始化完成
2025-08-07 10:04:43.739 | INFO     | src.modules.format_management.field_registry:__init__:95 | 🏷️ [字段注册] 字段注册系统初始化: state/data/field_mappings.json
2025-08-07 10:04:43.740 | INFO     | src.modules.format_management.format_renderer:__init__:83 | 🎨 [格式渲染] 格式渲染器初始化完成
2025-08-07 10:04:43.740 | INFO     | src.modules.format_management.unified_format_manager:_setup_event_listeners:877 | 🎯 [事件驱动] 事件监听器设置完成
2025-08-07 10:04:43.742 | INFO     | src.modules.format_management.unified_format_manager:_sync_to_state_manager:892 | 🎯 [统一状态管理] 状态同步完成
2025-08-07 10:04:43.742 | INFO     | src.modules.format_management.format_config:load_config:385 | 🔧 [格式配置] 配置文件不存在，创建默认配置
2025-08-07 10:04:43.743 | INFO     | src.modules.format_management.format_config:save_config:435 | 🔧 [格式配置] 配置文件保存成功
2025-08-07 10:04:43.744 | INFO     | src.modules.format_management.field_registry:load_mappings:628 | 🏷️ [字段注册] 字段映射加载成功
2025-08-07 10:04:43.744 | INFO     | src.modules.format_management.unified_format_manager:_initialize_system:146 | 🎯 [统一格式管理] 系统初始化完成
2025-08-07 10:04:43.744 | INFO     | src.modules.format_management.unified_format_manager:__init__:130 | 🎯 [统一格式管理] 统一格式管理器初始化完成（新架构版本）
2025-08-07 10:04:43.745 | INFO     | src.modules.format_management.unified_format_manager:__init__:1075 | 🔧 [单例优化] 单例统一格式管理器初始化完成
2025-08-07 10:04:43.745 | INFO     | src.modules.format_management.format_renderer:render_dataframe:107 | 🎯 [格式渲染] 隐藏字段配置: ['created_at', 'id', 'updated_at', 'sequence', 'row_number']
2025-08-07 10:04:43.746 | INFO     | src.modules.format_management.format_renderer:render_dataframe:115 | 🎯 [格式渲染] 无需隐藏字段（未在当前数据中找到）
2025-08-07 10:04:43.752 | INFO     | src.modules.format_management.format_renderer:render_dataframe:133 | [关键修复] 字段: 补发 -> 英文名: supplement -> 类型: float
2025-08-07 10:04:43.752 | INFO     | src.modules.format_management.format_renderer:render_dataframe:133 | [关键修复] 字段: 借支 -> 英文名: advance -> 类型: float
2025-08-07 10:04:43.754 | INFO     | src.modules.format_management.format_renderer:render_dataframe:133 | [关键修复] 字段: 月份 -> 英文名: month -> 类型: month_string
2025-08-07 10:04:43.754 | INFO     | src.modules.format_management.field_registry:get_display_fields:1553 | 🔧 [P0-2修复] display_order转换完成: 24个字段，原始字段数: 24
2025-08-07 10:04:43.755 | INFO     | src.modules.format_management.format_renderer:render_dataframe:153 | 🔧 [DEBUG] table_type=active_employees, display_fields=24个字段
2025-08-07 10:04:43.755 | INFO     | src.modules.format_management.format_renderer:render_dataframe:174 | 🔧 [DEBUG] formatted_df.columns=24个, existing_display_fields=24个
2025-08-07 10:04:43.756 | INFO     | src.modules.format_management.format_renderer:render_dataframe:178 | 🎯 [格式渲染] 已按display_order排列字段: 24个字段
2025-08-07 10:04:43.756 | INFO     | src.modules.format_management.format_renderer:render_dataframe:199 | 🎨 [格式渲染] DataFrame格式化完成: active_employees, 行数: 50, 列数: 24
2025-08-07 10:04:43.757 | INFO     | src.modules.format_management.unified_format_manager:format_data:363 | 🎯 [统一格式管理] 数据格式化完成: active_employees, 行数: 50, 列数: 24
2025-08-07 10:04:43.767 | INFO     | src.modules.format_management.format_config:load_config:403 | 🔧 [格式配置] 配置文件加载成功
2025-08-07 10:04:43.805 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:238 | [数据流追踪] 自动渲染策略选择: 50行数据
2025-08-07 10:04:43.805 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_small_dataset:51 | [数据流追踪] 开始小数据集渲染: 50行 x 24列
2025-08-07 10:04:43.810 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_small_dataset:104 | [数据流追踪] 小数据集渲染完成: 耗时5.0ms, 平均每行0.10ms
2025-08-07 10:04:43.813 | INFO     | src.core.performance_metrics_collector:__init__:74 | 性能度量收集器初始化完成，存储路径: logs\performance_metrics.json
2025-08-07 10:04:43.814 | INFO     | src.core.performance_metrics_collector:record_operation:85 | [数据流追踪] 记录性能度量: render, 数据大小=50, 渲染时间=5.0ms, 策略=small_dataset
2025-08-07 10:04:43.814 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:259 | [数据流追踪] 选择小数据集渲染策略
2025-08-07 10:04:43.815 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2778 | [数据流追踪] 优化渲染完成: 50行 x 24列, 策略=small_dataset, 耗时=5.0ms, 性能评级=excellent
2025-08-07 10:04:43.817 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:430 | 第0行数据工号: 19990089
2025-08-07 10:04:43.817 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:430 | 第1行数据工号: 20161565
2025-08-07 10:04:43.817 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:430 | 第2行数据工号: 20191782
2025-08-07 10:04:43.818 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:430 | 第3行数据工号: 20151515
2025-08-07 10:04:43.818 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:430 | 第4行数据工号: 20181640
2025-08-07 10:04:43.819 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:458 | 🚨 [UI数据修复] _update_visible_rows调用，数据行数: 50
2025-08-07 10:04:43.819 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:471 | 🚨 [UI数据修复] row_data[0]: 工号=19990089, 薪资=2375.00
2025-08-07 10:04:43.819 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:471 | 🚨 [UI数据修复] row_data[1]: 工号=20161565, 薪资=1696.00
2025-08-07 10:04:43.820 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:471 | 🚨 [UI数据修复] row_data[2]: 工号=20191782, 薪资=1427.00
2025-08-07 10:04:43.820 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:471 | 🚨 [UI数据修复] row_data[3]: 工号=20151515, 薪资=2175.00
2025-08-07 10:04:43.820 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:471 | 🚨 [UI数据修复] row_data[4]: 工号=20181640, 薪资=2582.00
2025-08-07 10:04:43.821 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:472 | 可见行数据顺序: ['19990089', '20161565', '20191782', '20151515', '20181640']
2025-08-07 10:04:43.821 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 表格数据已设置: 50 行, 24 列
2025-08-07 10:04:43.822 | INFO     | src.gui.prototype.widgets.column_sort_manager:update_table_context:409 | 🔧 [新架构] 排序管理器切换到表格: salary_data_2025_08_active_employees
2025-08-07 10:04:43.823 | INFO     | src.gui.prototype.widgets.column_sort_manager:_reload_field_mapping_for_table:440 | 🔧 [新架构] 为表格 salary_data_2025_08_active_employees 重新加载 28 个字段映射
2025-08-07 10:04:43.823 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2870 | 表格数据设置完成: 50 行, 耗时: 95.0ms
2025-08-07 10:04:43.824 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:7661 | 🔧 [P0-排序修复] 开始恢复排序指示器显示
2025-08-07 10:04:43.824 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:7674 | 🔧 [P0-排序修复] 无排序状态需要恢复
2025-08-07 10:04:43.831 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1694 | 🆕 [列宽保存] 未找到表格 salary_data_2025_08_active_employees 的列宽配置
2025-08-07 10:04:43.831 | INFO     | src.gui.widgets.pagination_widget:set_total_records:436 | 总记录数设置为: 50
2025-08-07 10:04:43.832 | INFO     | src.gui.prototype.prototype_main_window:set_data:847 | 🔧 [P0-CRITICAL修复] 非分页上下文，设置total_records=50
2025-08-07 10:04:43.832 | INFO     | src.gui.prototype.prototype_main_window:_on_pagination_data_loaded:6974 | 🔍 [调试-分页] 即将调用set_pagination_state: 第1页, 记录1-50
2025-08-07 10:04:43.832 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:3908 | 🔍 [表格调试] ================== set_pagination_state 开始 ==================
2025-08-07 10:04:43.833 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:3909 | 🔍 [表格调试] 收到分页状态参数: {'current_page': 1, 'page_size': 50, 'total_records': 1396, 'start_record': 1, 'end_record': 50}
2025-08-07 10:04:43.833 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:3910 | 🔍 [表格调试] 当前表格行数: 50
2025-08-07 10:04:43.834 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:3911 | 🔍 [表格调试] 当前表格列数: 24
2025-08-07 10:04:43.834 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:3913 | 🔧 [分页行号修复] 接收分页状态: 第1页, 记录1-50
2025-08-07 10:04:43.835 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1694 | 🆕 [列宽保存] 未找到表格 salary_data_2025_08_active_employees 的列宽配置
2025-08-07 10:04:43.835 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:3940 | 🔧 [P0-3修复] 分页时已恢复列宽设置: salary_data_2025_08_active_employees
2025-08-07 10:04:43.835 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:3948 | 🔧 [分页行号修复] 表格状态: 当前行数=50, 期望行数=50
2025-08-07 10:04:43.836 | INFO     | src.gui.prototype.widgets.header_update_manager:force_update_all:317 | 智能强制更新: 表头 24 个, 行号起始 1, 共 50 行
2025-08-07 10:04:43.836 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_force_update_pagination_row_numbers:4027 | 🔧 [分页行号修复] 强制更新完成: 起始记录1, 共50行
2025-08-07 10:04:43.837 | INFO     | src.gui.prototype.prototype_main_window:_on_pagination_data_loaded:6976 | 🔍 [调试-分页] set_pagination_state调用完成
2025-08-07 10:04:43.840 | INFO     | src.gui.widgets.pagination_widget:set_total_records:436 | 总记录数设置为: 1396
2025-08-07 10:04:43.841 | INFO     | src.gui.prototype.prototype_main_window:_on_pagination_data_loaded:6998 | 🔧 [P0-新1修复] 分页状态验证: 当前第1页，共28页，总记录1396条
2025-08-07 10:04:43.843 | INFO     | src.gui.prototype.prototype_main_window:_on_pagination_data_loaded:7049 | 🆕 [新架构] 分页数据加载完成，已完成渐进式状态迁移
2025-08-07 10:04:43.931 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_ui_state_after_data_set:7696 | 🔧 [P0-2修复] 数据设置后UI状态恢复完成: salary_data_2025_08_active_employees
2025-08-07 10:04:46.825 | INFO     | src.gui.prototype.prototype_main_window:_fix_display_brightness_after_data_refresh:8018 | 🔧 [P1-2] 开始智能显示亮度修复（增强防护）
2025-08-07 10:04:55.957 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_save_column_widths:1659 | 🔧 [列宽保存修复] 准备保存列宽到文件: E:\project\case\salary_changes\salary_changes\state\column_widths.json
2025-08-07 10:04:55.957 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_save_column_widths:1660 | 🔧 [列宽保存修复] 表名: salary_data_2025_08_active_employees, 列数: 24
2025-08-07 10:04:55.959 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_save_column_widths:1665 | 🔧 [列宽保存修复] 列宽设置保存成功！文件大小: 1907 字节
2025-08-07 10:04:56.741 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_save_column_widths:1659 | 🔧 [列宽保存修复] 准备保存列宽到文件: E:\project\case\salary_changes\salary_changes\state\column_widths.json
2025-08-07 10:04:56.742 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_save_column_widths:1660 | 🔧 [列宽保存修复] 表名: salary_data_2025_08_active_employees, 列数: 24
2025-08-07 10:04:56.745 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_save_column_widths:1665 | 🔧 [列宽保存修复] 列宽设置保存成功！文件大小: 1908 字节
2025-08-07 10:04:59.346 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_save_column_widths:1659 | 🔧 [列宽保存修复] 准备保存列宽到文件: E:\project\case\salary_changes\salary_changes\state\column_widths.json
2025-08-07 10:04:59.347 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_save_column_widths:1660 | 🔧 [列宽保存修复] 表名: salary_data_2025_08_active_employees, 列数: 24
2025-08-07 10:04:59.351 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_save_column_widths:1665 | 🔧 [列宽保存修复] 列宽设置保存成功！文件大小: 1907 字节
2025-08-07 10:05:01.920 | INFO     | src.gui.prototype.widgets.column_sort_manager:handle_header_click:132 | 🆕 [多列排序] 新增列5(2025年岗位工资)排序: 升序
2025-08-07 10:05:01.920 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_on_sort_state_changed:7161 | 🆕 [新架构多列排序] 排序状态变化: 1 列
2025-08-07 10:05:01.922 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_on_sort_state_changed:7169 | 🆕 [新架构多列排序] 当前排序: 2025年岗位工资: 升序
2025-08-07 10:05:01.922 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_on_sort_request:7184 | 🆕 [新架构多列排序] 排序请求: salary_data_2025_08_active_employees.position_salary_2025 -> ascending
2025-08-07 10:05:01.924 | INFO     | src.gui.prototype.prototype_main_window:_handle_sort_applied:4130 | 🆕 [新架构排序] 处理排序应用: 列5, position_salary_2025, ascending
2025-08-07 10:05:01.927 | INFO     | src.core.table_sort_state_manager:save_sort_state:229 | 已保存排序状态: salary_data_2025_08_active_employees (employees), 1 列
2025-08-07 10:05:01.927 | INFO     | src.gui.prototype.prototype_main_window:_handle_sort_applied:4170 | 表头排序状态已同步到管理器
2025-08-07 10:05:01.928 | INFO     | src.gui.prototype.prototype_main_window:_handle_sort_applied:4177 | 排序时保持页码: 1
2025-08-07 10:05:01.928 | INFO     | src.gui.prototype.prototype_main_window:_publish_sort_request_event:3840 | 🔧 [排序调试] 准备发布排序请求: salary_data_2025_08_active_employees, [{'column_name': 'position_salary_2025', 'order': 'ascending', 'priority': 0}]
2025-08-07 10:05:01.929 | INFO     | src.gui.prototype.prototype_main_window:_publish_sort_request_event:3871 | [数据流追踪] 开始处理排序请求: 表=salary_data_2025_08_active_employees, 排序列数=1, 页码=1
2025-08-07 10:05:01.929 | INFO     | src.gui.prototype.prototype_main_window:_publish_sort_request_event:3919 | 🔧 [排序] 使用实际页码: 1 (传入页码: 1)
2025-08-07 10:05:01.933 | INFO     | src.services.table_data_service:_handle_sort_request:147 | [排序调试] TableDataService接收到排序请求: salary_data_2025_08_active_employees
2025-08-07 10:05:01.933 | INFO     | src.gui.prototype.prototype_main_window:_publish_sort_request_event:3958 | [数据流追踪] 排序事件已发布: 表=salary_data_2025_08_active_employees, 排序列数=1, 页码=1
2025-08-07 10:05:01.934 | INFO     | src.services.table_data_service:_handle_sort_request:148 | [排序调试] 排序列: [{'column_name': 'position_salary_2025', 'order': 'ascending', 'priority': 0}]
2025-08-07 10:05:01.934 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_on_header_clicked:7872 | 🆕 [新架构多列排序] 成功处理列5(2025年岗位工资)点击
2025-08-07 10:05:01.935 | INFO     | src.services.table_data_service:_handle_sort_request:149 | [排序调试] 当前页: 1
2025-08-07 10:05:01.936 | INFO     | src.services.table_data_service:_handle_sort_request:179 | [排序调试] 执行数据请求: DataRequest(table_name='salary_data_2025_08_active_employees', request_type=<RequestType.SORT_CHANGE: 'sort_change'>, page=1, page_size=50, sort_columns=[{'column_name': 'position_salary_2025', 'order': 'ascending', 'priority': 0}], filters=None, selected_fields=None, preserve_page=True, preserve_sort=True, preserve_filters=True, request_id='req_1754532301.936175', timestamp=datetime.datetime(2025, 8, 7, 10, 5, 1, 936175))
2025-08-07 10:05:01.937 | INFO     | src.core.unified_data_request_manager:request_table_data:203 | 开始处理数据请求: salary_data_2025_08_active_employees, 类型: sort_change
2025-08-07 10:05:01.938 | INFO     | src.modules.data_storage.dynamic_table_manager:_column_exists_in_table:642 | [FIX] [排序修复] 列 'position_salary_2025' 存在于表 'salary_data_2025_08_active_employees' 中（类型: REAL）
2025-08-07 10:05:01.939 | INFO     | src.modules.data_storage.dynamic_table_manager:_column_exists_in_table:642 | [FIX] [排序修复] 列 'position_salary_2025' 存在于表 'salary_data_2025_08_active_employees' 中（类型: REAL）
2025-08-07 10:05:01.940 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:487 | 正在从表 salary_data_2025_08_active_employees 分页获取数据（支持排序）: 第1页, 每页50条, 排序=1列
2025-08-07 10:05:01.940 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:516 | [排序修复] 准备排序列: position_salary_2025
2025-08-07 10:05:01.940 | INFO     | src.modules.data_storage.dynamic_table_manager:_column_exists_in_table:642 | [FIX] [排序修复] 列 'position_salary_2025' 存在于表 'salary_data_2025_08_active_employees' 中（类型: REAL）
2025-08-07 10:05:01.941 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:523 | [排序修复] 列 'position_salary_2025' 验证通过，开始排序
2025-08-07 10:05:01.941 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:532 | [FIX] [排序修复] 数值列排序: position_salary_2025 ASC
2025-08-07 10:05:01.945 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:539 | [FIX] [排序修复] 添加排序子句: CAST("position_salary_2025" AS REAL) ASC
2025-08-07 10:05:01.946 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:547 | [P0-CRITICAL修复] 执行的完整SQL查询: SELECT * FROM "salary_data_2025_08_active_employees" ORDER BY CAST("position_salary_2025" AS REAL) ASC LIMIT 50 OFFSET 0
2025-08-07 10:05:01.948 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:557 | [P0-CRITICAL修复] 数据库查询返回的前5个工号: ['20191787.0', '20241009.0', '20021464.0', '20121440.0', '20251012.0']
2025-08-07 10:05:01.949 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:566 | [FIX] [P0-CRITICAL修复] 查询结果中 position_salary_2025 的前10个值: [1730.0, 1765.0, 1765.0, 1765.0, 1765.0, 1765.0, 1765.0, 1765.0, 1765.0, 1765.0]
2025-08-07 10:05:01.950 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:569 | 成功从表 salary_data_2025_08_active_employees 获取第1页数据（含排序）: 50 行，总计1396行
2025-08-07 10:05:01.951 | INFO     | src.core.unified_data_request_manager:request_table_data:252 | 数据请求处理完成: 28字段, 50行, 耗时14.6ms
2025-08-07 10:05:01.952 | INFO     | src.services.table_data_service:_handle_sort_request:188 | [排序调试] 数据请求响应: 成功=True
2025-08-07 10:05:01.952 | INFO     | src.services.table_data_service:_handle_sort_request:192 | [排序调试] 排序数据获取成功: 50行
2025-08-07 10:05:01.952 | INFO     | src.services.table_data_service:_handle_sort_request:193 | [修复排序] 排序操作成功，发布数据更新事件: salary_data_2025_08_active_employees, 50行
2025-08-07 10:05:01.953 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3432 | 🆕 接收到新架构数据更新事件: salary_data_2025_08_active_employees
2025-08-07 10:05:01.953 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3457 | 数据内容: 50行 x 28列
2025-08-07 10:05:01.958 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3480 | 🔧 [关键修复] 接收到sort_change操作的数据更新事件，开始UI更新: salary_data_2025_08_active_employees, 50行
2025-08-07 10:05:01.959 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:6501 | 表 salary_data_2025_08_active_employees 没有用户偏好设置，显示所有可见字段
2025-08-07 10:05:01.982 | INFO     | src.gui.prototype.prototype_main_window:set_data:763 | 通过公共接口设置新的表格数据(DataFrame): 50 行
2025-08-07 10:05:01.985 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:6501 | 表 salary_data_2025_08_active_employees 没有用户偏好设置，显示所有可见字段
2025-08-07 10:05:01.987 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2612 | 🚨 [UI数据修复] 保存到original_data第0行: 工号=20191787.0, 薪资=999.0
2025-08-07 10:05:01.987 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2612 | 🚨 [UI数据修复] 保存到original_data第1行: 工号=20241009.0, 薪资=1339.0
2025-08-07 10:05:01.987 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:5011 | 最大可见行数已更新: 50 -> 50
2025-08-07 10:05:01.988 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:5064 | 根据数据量(50)自动调整最大可见行数为: 50
2025-08-07 10:05:01.991 | INFO     | src.modules.format_management.format_renderer:render_dataframe:107 | 🎯 [格式渲染] 隐藏字段配置: ['created_at', 'id', 'updated_at', 'sequence', 'row_number']
2025-08-07 10:05:01.991 | INFO     | src.modules.format_management.format_renderer:render_dataframe:115 | 🎯 [格式渲染] 无需隐藏字段（未在当前数据中找到）
2025-08-07 10:05:01.996 | INFO     | src.modules.format_management.format_renderer:render_dataframe:133 | [关键修复] 字段: 补发 -> 英文名: supplement -> 类型: float
2025-08-07 10:05:01.996 | INFO     | src.modules.format_management.format_renderer:render_dataframe:133 | [关键修复] 字段: 借支 -> 英文名: advance -> 类型: float
2025-08-07 10:05:01.998 | INFO     | src.modules.format_management.format_renderer:render_dataframe:133 | [关键修复] 字段: 月份 -> 英文名: month -> 类型: month_string
2025-08-07 10:05:01.998 | INFO     | src.modules.format_management.field_registry:get_display_fields:1553 | 🔧 [P0-2修复] display_order转换完成: 24个字段，原始字段数: 24
2025-08-07 10:05:01.999 | INFO     | src.modules.format_management.format_renderer:render_dataframe:153 | 🔧 [DEBUG] table_type=active_employees, display_fields=24个字段
2025-08-07 10:05:01.999 | INFO     | src.modules.format_management.format_renderer:render_dataframe:174 | 🔧 [DEBUG] formatted_df.columns=24个, existing_display_fields=24个
2025-08-07 10:05:02.000 | INFO     | src.modules.format_management.format_renderer:render_dataframe:178 | 🎯 [格式渲染] 已按display_order排列字段: 24个字段
2025-08-07 10:05:02.000 | INFO     | src.modules.format_management.format_renderer:render_dataframe:199 | 🎨 [格式渲染] DataFrame格式化完成: active_employees, 行数: 50, 列数: 24
2025-08-07 10:05:02.001 | INFO     | src.modules.format_management.unified_format_manager:format_data:363 | 🎯 [统一格式管理] 数据格式化完成: active_employees, 行数: 50, 列数: 24
2025-08-07 10:05:02.025 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:238 | [数据流追踪] 自动渲染策略选择: 50行数据
2025-08-07 10:05:02.025 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_small_dataset:51 | [数据流追踪] 开始小数据集渲染: 50行 x 24列
2025-08-07 10:05:02.042 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_small_dataset:104 | [数据流追踪] 小数据集渲染完成: 耗时17.0ms, 平均每行0.34ms
2025-08-07 10:05:02.042 | INFO     | src.core.performance_metrics_collector:record_operation:85 | [数据流追踪] 记录性能度量: render, 数据大小=50, 渲染时间=17.0ms, 策略=small_dataset
2025-08-07 10:05:02.043 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:259 | [数据流追踪] 选择小数据集渲染策略
2025-08-07 10:05:02.043 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2778 | [数据流追踪] 优化渲染完成: 50行 x 24列, 策略=small_dataset, 耗时=17.0ms, 性能评级=excellent
2025-08-07 10:05:02.044 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:430 | 第0行数据工号: 20191787
2025-08-07 10:05:02.044 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:430 | 第1行数据工号: 20241009
2025-08-07 10:05:02.044 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:430 | 第2行数据工号: 20021464
2025-08-07 10:05:02.045 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:430 | 第3行数据工号: 20121440
2025-08-07 10:05:02.045 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:430 | 第4行数据工号: 20251012
2025-08-07 10:05:02.046 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:458 | 🚨 [UI数据修复] _update_visible_rows调用，数据行数: 50
2025-08-07 10:05:02.046 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:471 | 🚨 [UI数据修复] row_data[0]: 工号=20191787, 薪资=999.00
2025-08-07 10:05:02.046 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:471 | 🚨 [UI数据修复] row_data[1]: 工号=20241009, 薪资=1339.00
2025-08-07 10:05:02.047 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:471 | 🚨 [UI数据修复] row_data[2]: 工号=20021464, 薪资=2175.00
2025-08-07 10:05:02.047 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:471 | 🚨 [UI数据修复] row_data[3]: 工号=20121440, 薪资=1427.00
2025-08-07 10:05:02.047 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:471 | 🚨 [UI数据修复] row_data[4]: 工号=20251012, 薪资=934.00
2025-08-07 10:05:02.048 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:472 | 可见行数据顺序: ['20191787', '20241009', '20021464', '20121440', '20251012']
2025-08-07 10:05:02.048 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 表格数据已设置: 50 行, 24 列
2025-08-07 10:05:02.049 | WARNING  | src.utils.thread_safe_timer:is_main_thread:63 | 🔧 [线程检查] 当前线程不是主线程: ThreadPoolExecutor-2_0
2025-08-07 10:05:02.049 | WARNING  | src.utils.thread_safe_timer:safe_single_shot:158 | 🔧 [线程安全] 尝试在非主线程使用singleShot，将移动到主线程执行
2025-08-07 10:05:02.049 | WARNING  | src.utils.thread_safe_timer:is_main_thread:63 | 🔧 [线程检查] 当前线程不是主线程: ThreadPoolExecutor-2_0
2025-08-07 10:05:02.055 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2870 | 表格数据设置完成: 50 行, 耗时: 69.0ms
2025-08-07 10:05:02.056 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:7661 | 🔧 [P0-排序修复] 开始恢复排序指示器显示
2025-08-07 10:05:02.056 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:7671 | 🔧 [P0-排序修复] 恢复排序指示器成功: 1 列排序
2025-08-07 10:05:02.057 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:7672 | 🔧 [P0-排序修复] 排序描述: 2025年岗位工资: 升序
2025-08-07 10:05:02.068 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1719 | 列宽已恢复: salary_data_2025_08_active_employees (24/24 列)
2025-08-07 10:05:02.068 | WARNING  | src.utils.thread_safe_timer:is_main_thread:63 | 🔧 [线程检查] 当前线程不是主线程: ThreadPoolExecutor-2_0
2025-08-07 10:05:02.069 | WARNING  | src.utils.thread_safe_timer:safe_single_shot:158 | 🔧 [线程安全] 尝试在非主线程使用singleShot，将移动到主线程执行
2025-08-07 10:05:02.069 | WARNING  | src.utils.thread_safe_timer:is_main_thread:63 | 🔧 [线程检查] 当前线程不是主线程: ThreadPoolExecutor-2_0
2025-08-07 10:05:02.071 | INFO     | src.gui.widgets.pagination_widget:set_total_records:436 | 总记录数设置为: 50
2025-08-07 10:05:02.071 | INFO     | src.gui.prototype.prototype_main_window:set_data:847 | 🔧 [P0-CRITICAL修复] 非分页上下文，设置total_records=50
2025-08-07 10:05:02.077 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3515 | 🔧 [P1-1修复] 数据已成功设置到UI，使用新映射表头: 50行, 24列
2025-08-07 10:05:02.077 | INFO     | src.gui.prototype.widgets.header_update_manager:force_update_all:317 | 智能强制更新: 表头 24 个, 行号起始 1, 共 50 行
2025-08-07 10:05:02.078 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3531 | 🔧 [P0-新5修复] 表头强制刷新完成: salary_data_2025_08_active_employees, 传递参数: 24个表头
2025-08-07 10:05:02.078 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3535 | 🔧 [统一数据设置] 数据已成功设置到UI: 50行, 24列
2025-08-07 10:05:02.078 | INFO     | src.gui.widgets.pagination_widget:set_total_records:436 | 总记录数设置为: 1396
2025-08-07 10:05:02.079 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3558 | 🔧 [分页修复] 数据更新事件设置总记录数: 1396
2025-08-07 10:05:02.079 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3565 | 🔧 [CRITICAL修复] 排序操作保持当前页码: 1，不进行页码重置
