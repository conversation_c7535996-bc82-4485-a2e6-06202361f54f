#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🚨 表头排序崩溃修复验证脚本

直接验证修复效果，确保程序不再因排序而崩溃
"""

import sys
import time
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

def check_critical_fixes():
    """检查关键修复点是否已应用"""
    print("🔍 检查关键修复点...")
    
    table_file = project_root / "src" / "gui" / "prototype" / "widgets" / "virtualized_expandable_table.py"
    
    if not table_file.exists():
        print("❌ 表格文件不存在")
        return False
    
    with open(table_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 关键修复点检查
    critical_fixes = [
        "🚨 [崩溃修复] 使用QTimer延迟调用set_data",
        "🚨 [崩溃修复] 最高优先级：防止程序崩溃的全面保护", 
        "QThread.currentThread() == QApplication.instance().thread()",
        "delayed_set_data()",
        "delayed_update_pagination()"
    ]
    
    missing = []
    for fix in critical_fixes:
        if fix not in content:
            missing.append(fix)
    
    if missing:
        print("❌ 缺少关键修复:")
        for m in missing:
            print(f"   - {m}")
        return False
    
    print("✅ 所有关键修复点已应用")
    return True

def test_qt_safety():
    """测试Qt线程安全"""
    print("🔧 测试Qt线程安全...")
    
    try:
        from PyQt5.QtWidgets import QApplication
        from PyQt5.QtCore import QTimer, QThread, QMetaObject
        
        app = QApplication.instance()
        if not app:
            app = QApplication(sys.argv)
        
        # 测试线程检查
        main_thread = app.thread()
        current_thread = QThread.currentThread()
        
        print(f"主线程: {main_thread}")
        print(f"当前线程: {current_thread}")
        print(f"线程匹配: {current_thread == main_thread}")
        
        # 测试QTimer创建
        timer = QTimer()
        timer.setSingleShot(True)
        
        test_passed = [False]
        def timer_callback():
            test_passed[0] = True
            print("✅ QTimer测试成功")
        
        timer.timeout.connect(timer_callback)
        timer.start(50)
        
        # 等待定时器执行
        start_time = time.time()
        while not test_passed[0] and time.time() - start_time < 1:
            app.processEvents()
            time.sleep(0.01)
        
        if not test_passed[0]:
            print("❌ QTimer测试失败")
            return False
        
        print("✅ Qt线程安全测试通过")
        return True
        
    except Exception as e:
        print(f"❌ Qt测试失败: {e}")
        return False

def generate_fix_summary():
    """生成修复总结"""
    print("\n📋 生成修复总结...")
    
    summary = f"""
# 🚨 表头排序崩溃修复总结

## 修复时间
{time.strftime('%Y-%m-%d %H:%M:%S')}

## 问题描述
用户导入数据后，调整表头宽度，点击表头排序时程序异常退出，无任何错误提示。

## 根本原因
1. **Qt事件冲突**: 在表头点击事件处理中直接调用 `set_data()` 导致Qt底层崩溃
2. **线程安全问题**: QTimer在非主线程中创建和使用
3. **异常处理不足**: 缺乏最外层异常捕获机制

## 修复措施

### 1. 延迟执行关键操作
- ✅ 使用 `QTimer.singleShot()` 延迟执行 `set_data()`
- ✅ 延迟执行分页组件更新
- ✅ 避免在事件处理中直接操作UI组件

### 2. 线程安全保护
- ✅ 检查当前线程是否为主线程
- ✅ 使用 `QMetaObject.invokeMethod()` 跨线程调用
- ✅ 定时器创建失败时的降级处理

### 3. 异常处理增强
- ✅ 多层异常捕获机制
- ✅ 最外层异常保护，确保程序不崩溃
- ✅ 用户友好的错误提示对话框

## 预期效果
1. ✅ 表头点击排序不再导致程序崩溃
2. ✅ 排序失败时显示友好提示，程序继续运行
3. ✅ 提高系统整体稳定性

## 测试步骤
1. 启动程序，导入数据
2. 调整任意表头宽度
3. 点击表头进行排序
4. 观察程序是否稳定运行

## 风险评估
- 🟢 **低风险**: 主要添加保护机制，不改变核心逻辑
- 🟢 **向后兼容**: 不影响现有功能
- 🟢 **可快速回滚**: 如有问题可立即回滚
"""
    
    # 保存总结
    summary_file = project_root / "docs" / "problems" / "表头排序崩溃修复总结.md"
    summary_file.parent.mkdir(parents=True, exist_ok=True)
    
    with open(summary_file, 'w', encoding='utf-8') as f:
        f.write(summary)
    
    print(f"✅ 修复总结已保存: {summary_file}")

def main():
    """主函数"""
    print("🚨 表头排序崩溃修复验证")
    print("=" * 40)
    
    success = True
    
    # 检查修复点
    if not check_critical_fixes():
        success = False
    
    # 测试Qt安全性
    if not test_qt_safety():
        success = False
    
    # 生成总结
    generate_fix_summary()
    
    if success:
        print("\n✅ 修复验证成功！")
        print("🎯 现在可以测试：导入数据 → 调整表头 → 点击排序")
        print("📋 预期：程序稳定运行，不再崩溃")
    else:
        print("\n❌ 修复验证失败！")
        print("🔧 请检查修复是否正确应用")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
