{"salary_data_2025_08_active_employees": {"table_name": "salary_data_2025_08_active_employees", "table_type": "employees", "sort_columns": [{"column_name": "position_salary_2025", "order": "ascending", "priority": 0}], "field_mapping": {"sequence_number": "序号", "employee_id": "工号", "employee_name": "姓名", "department": "部门名称", "employee_type_code": "人员类别代码", "employee_type": "人员类别", "position_salary_2025": "2025年岗位工资", "grade_salary_2025": "2025年薪级工资", "allowance": "津贴", "balance_allowance": "结余津贴", "basic_performance_2025": "2025年基础性绩效", "health_fee": "卫生费", "transport_allowance": "交通补贴", "property_allowance": "物业补贴", "housing_allowance": "住房补贴", "car_allowance": "车补", "communication_allowance": "通讯补贴", "performance_bonus_2025": "2025年奖励性绩效预发", "supplement": "补发", "advance": "借支", "total_salary": "应发工资", "provident_fund_2025": "2025公积金", "pension_insurance": "代扣代存养老保险", "id": "自增主键", "month": "月份", "year": "年份", "created_at": "创建时间", "updated_at": "更新时间"}, "is_global_sort": false, "status": "dirty", "created_at": **********.9250274, "last_updated": **********.9250274, "last_accessed": **********.9250274, "current_page": 1, "page_size": 50, "total_records": 0, "session_id": null}}